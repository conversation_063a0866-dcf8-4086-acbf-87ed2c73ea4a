// @ts-check

import routes from "../routes.js";
import IntlProvider from "./IntlProvider.js";
import TemplateLocale from "../models/TemplateLocale.js";
import ProductLocale from "../models/ProductLocale.js";
import { resolveHostname } from "../host.js";
import { getBundle, getTemplateOrDefault } from "../database.js";
import getViewOrDefault from "../utils/views.js";
import { base64URLEncode } from "../utils/base64URL.js";
import fbTranslations from "../i18n/fallback-desktop.js";
import getUserLanguage from "../utils/user-language.js";

export default async function bundle(req, res) {
  const originUrl = new URL(req.url, `${req.protocol}://${req.hostname}`);
  const bund = await getBundle(req.uid);

  if (!bund || !bund.organization || bund.organization.status === "inactive") {
    return res.callNotFound();
  }

  const { products } = bund;
  const template = await getTemplateOrDefault(bund.organization.templateId);

  if (!template) {
    return res.callNotFound();
  }

  const intl = new IntlProvider({
    browserAcceptLanguage: req.headers["accept-language"],
    defaultLanguage: products[0]?.defaultLang || "fr",
  });

  /** @type {TemplateLocale} */
  const templateLocale = intl.findBestOr(
    template.locales,
    TemplateLocale.default(),
  );
  const host = resolveHostname();

  const localizedProducts = products.reduce(
    (/** @type {Array<Object>} */ result, product) => {
      if (product) {
        const productLocale = intl.findBestOr(
          product.locales,
          ProductLocale.default(),
        );

        let arButtonLink = "";

        if (productLocale.link) {
          const base64Data = base64URLEncode(JSON.stringify({
            uid: product.uid,
            link: req.query.link || productLocale.link,
          }));

          arButtonLink = `${host}/${routes.arRedirect}/${base64Data}`;
        }

        result.push({
          name: product.name,
          code: product.code,
          src: product.glbUrl,
          title: productLocale.title,
          occlusion: product.occlusion,
          fallbackUrl: `${host}/${routes.fallback}/${product.uid}`,
          link: arButtonLink,
          iosSrc: product.usdzUrl,
          checkoutTitle: productLocale.checkoutTitle,
          checkoutSubtitle: productLocale.checkoutSubtitle,
          callToAction: productLocale.callToAction,
          iosLink: arButtonLink,
          price: productLocale.price,
          allowsContentScaling: product.allowsContentScaling,
          canonicalWebPageUrl: productLocale.canonicalWebPageUrl ||
            originUrl.href,
          buttonName: productLocale.buttonName,
        });
      }
      return result;
    },
    [],
  );

  const fallbackTranslations = getUserLanguage(req, fbTranslations);

  const viewProps = {
    orgName: bund.organization.name,
    analyticsId: bund.organization.analyticsId,
    bundleName: bund.name,
    logo: bund.organization.logoUrl,
    favicon: bund.organization.faviconUrl,
    webclip: bund.organization.webclipUrl,
    ...templateLocale,
    products: localizedProducts,
    fallbackTranslations,
  };

  return res.view(await getViewOrDefault("bundles", template.name), viewProps);
}
