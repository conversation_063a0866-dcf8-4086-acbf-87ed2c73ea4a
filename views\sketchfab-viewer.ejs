<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Wonder Shop - <%= name %></title>
    <link rel="stylesheet" href="../css/reset.css" />

    <script
      type="text/javascript"
      src="https://static.sketchfab.com/api/sketchfab-viewer-1.12.1.js"></script>

    <% if (locals.analyticsId) { %>
    <script
      defer
      src="<%= process.env.UMAMI_HOST + '/script.js' %>"
      data-website-id="<%= analyticsId %>"></script>
    <% } %>
  </head>

  <style>
    body {
      overflow: hidden;
    }

    .sketchfab-embed-wrapper iframe {
      width: 100%;
      height: 100vh;
    }
  </style>

  <body>
    <div class="sketchfab-embed-wrapper">
      <iframe
        id="viewer"
        frameborder="0"
        allowfullscreen
        mozallowfullscreen="true"
        webkitallowfullscreen="true"
        allow="autoplay; fullscreen; xr-spatial-tracking"
        xr-spatial-tracking
        execution-while-out-of-viewport
        execution-while-not-rendered
        web-share>
      </iframe>
    </div>

    <script>
      window.addEventListener("DOMContentLoaded", () => {
        const umami = window.umami;
        const viewerIframe = document.getElementById("viewer");
        const productEventPayload = {
          code: "<%= productCode %>",
          name: "<%= productName %>",
        };
        const viewerOptions = {
          autostart: 1,
          camera: 0,
          ui_animations: 0,
          ui_infos: 0,
          ui_stop: 0,
          ui_inspector: 0,
          ui_watermark_link: 0,
          ui_watermark: 0,
          ui_ar: 0,
          ui_help: 0,
          ui_settings: 0,
          ui_vr: 0,
          ui_fullscreen: 0,
          ui_annotations: 0,
        };

        // Flag to prevent first camera start event that happens on load for some reason.
        let firstInteract = true;
        let viewerInteract = false;

        umami.track("viewer-impression", productEventPayload);

        function onSuccess(api) {
          api.addEventListener("camerastart", () => {
            if (firstInteract) {
              firstInteract = false;
              return;
            }

            if (viewerInteract) return;

            umami.track("viewer-interact", productEventPayload);
            viewerInteract = true;
          });
        }

        const client = new Sketchfab("1.12.1", viewerIframe);
        client.init("<%= id %>", {
          ...viewerOptions,
          success: onSuccess,
          error: (error) => {
            console.error("Error initializing viewer:", error);
          },
        });
      });
    </script>
  </body>
</html>
