// @ts-check

export default class TemplateLocale {
  /**
   * @param {Object} config
   * @param {string} config.code
   * @param {string} config.headline
   * @param {string} config.buttonName
   * @param {string} config.instructionTitle
   * @param {string} config.instruction1
   * @param {string} config.instruction2
   * @param {string} config.instruction3
   * @param {string} config.fallbackTitle
   * @param {string} config.fallbackSubtitle
   * @param {string} config.fallbackViewer
   * @param {string} config.desktopTitle
   */
  constructor({
    code,
    headline,
    buttonName,
    instructionTitle,
    instruction1,
    instruction2,
    instruction3,
    fallbackTitle,
    fallbackSubtitle,
    fallbackViewer,
    desktopTitle,
  }) {
    this.code = code;
    this.headline = headline;
    this.buttonName = buttonName;
    this.instructionTitle = instructionTitle;
    this.instruction1 = instruction1;
    this.instruction2 = instruction2;
    this.instruction3 = instruction3;
    this.fallbackTitle = fallbackTitle;
    this.fallbackSubtitle = fallbackSubtitle;
    this.fallbackViewer = fallbackViewer;
    this.desktopTitle = desktopTitle;
  }

  /**
   * @param {*} data
   * @returns {TemplateLocale}
   */
  static from(data) {
    return new TemplateLocale({
      code: data.code,
      headline: data.headline,
      buttonName: data.button_name,
      instructionTitle: data.instruction_title,
      instruction1: data.instruction_1,
      instruction2: data.instruction_2,
      instruction3: data.instruction_3,
      fallbackTitle: data.fallback_title,
      fallbackSubtitle: data.fallback_subtitle,
      fallbackViewer: data.fallback_viewer,
      desktopTitle: data.desktop_title,
    });
  }

  /**
   * @returns {TemplateLocale}
   */
  static default() {
    return new TemplateLocale({
      code: 'fr',
      headline: '',
      instructionTitle: '',
      instruction1: '',
      instruction2: '',
      instruction3: '',
      buttonName: '',
      fallbackTitle: '',
      fallbackSubtitle: '',
      fallbackViewer: '',
      desktopTitle: '',
    });
  }
}
