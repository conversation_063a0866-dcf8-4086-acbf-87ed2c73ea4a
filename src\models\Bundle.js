// @ts-check

import Organization from './Organization.js';
import Product from './Product.js';

export default class Bundle {
  /**
   * @param {string} uid
   * @param {string} name
   * @param {Organization} organization
   * @param {Array<Product>} products
   */
  constructor(uid, name, organization, products) {
    this.uid = uid;
    this.name = name;
    this.organization = organization;
    this.products = products;
  }

  /**
   * @param {*} data
   * @returns {Bundle}
   */
  static from(data) {
    return new Bundle(
      data.uid,
      data.name,
      Organization.from(data.organization),
      data.products.map((p) => Product.from(p)),
    );
  }
}
