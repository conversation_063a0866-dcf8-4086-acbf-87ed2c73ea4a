:root {
  --ar-icon-size: 1.56rem;
  --wonder-blue: #0094aa;
  --ar-button-hover: #42625f;
}

@font-face {
  font-family: "Poppins";
  src: url(/fonts/poppins/Poppins-SemiBold.ttf);
  font-weight: 600;
}

@font-face {
  font-family: "SourceSans3";
  src: url(/fonts/sourcesans/SourceSans3-Bold.ttf) format("truetype");
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "SourceSans3";
  src: url(/fonts/sourcesans/SourceSans3-Medium.ttf) format("truetype");
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

* {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  color: white;
}

html {
  height: 100vh;
  height: 100dvh;
  width: 100%;
  background-color: #fff;
}

body {
  margin: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  min-height: 100%;
}

.content-mobile {
  min-height: 90vh;
  flex: 1 0 auto;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.logo-container {
  margin: 1vh 10vw;
  padding: .5rem 1rem;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.logo-container p {
  text-align: center;
  margin-top: 1rem;
  font-family: 'SourceSans3';
  font-weight: 700;
  font-size: 1rem;
  line-height: 1.5rem;
  color: #254f9b;
}

.title {
  font-family: 'SourceSans3';
  font-weight: 700;
  font-size: 1.25rem;
  line-height: 1.5rem;
  color: #254f9b;
  padding-bottom: 0.75rem;
  margin: 1.25rem 0 0;
}

.separator {
  background-color: #c20016;
  height: 1px;
  width: 100%;
  /* margin-bottom: 0.75rem; */
}

a {
  text-decoration: none;
}

.viewer-container {
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
}

.button {
  width: 15.75rem;
  height: 3.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  background-color: #5bb9be;
  border: solid 2px #fff;
  border-radius: 50px;
  font-family: "Poppins";
  font-weight: 600;
  font-size: 1rem;
}

.button:hover {
  background-color: var(--ar-button-hover);
}

.sketchfab-embed-wrapper {
  width: 100%;
  background-color: #fff;
  margin-bottom: 2rem;
}

.sketchfab-embed-wrapper iframe {
  width: 100%;
  min-height: 400px;
  height: 100%;
}

