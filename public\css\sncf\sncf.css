:root {
  --ar-icon-size: 1.75rem;
  --dark-grey: #1c1c1c;
  --sncf-blue: #2085ca;
}

@font-face {
  font-family: "Poppins";
  src: url(/fonts/poppins/Poppins-SemiBold.ttf);
  font-weight: 600;
}

@font-face {
  font-family: "Trueno";
  src: url(/fonts/trueno/TruenoLt.ttf) format("truetype");
  font-weight: 400;
}

@font-face {
  font-family: "Trueno";
  src: url(/fonts/trueno/TruenoSBd.ttf) format("truetype");
  font-weight: 600;
}

body {
  font-size: 0.9rem;
}

.product-name {
  font-family: "Poppins";
  font-weight: 600;
}

.name-container {
  background-color: rgba(255, 255, 255, 0.2);
  color: var(--dark-grey);
  font-family: "Poppins";
  padding: 0.9rem 0;
}

.headline {
  width: 80vw;
  font-family: "Trueno";
  font-weight: 400;
  font-size: 1.625rem;
  color: var(--dark-grey);
  margin-top: 5.6rem;
}

.instruction-container {
  align-items: center;
  font-family: "Trueno";
  font-size: 14px;
  letter-spacing: 0.02rem;
}

.main-container {
  gap: calc((100vh * 10 / 100) - 1rem);
  padding-bottom: 5rem;
  margin-bottom: 1rem;
}

.main-container:before {
  content: " ";
  display: block;
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  opacity: 0.2;
  background-image: url("https://firebasestorage.googleapis.com/v0/b/sncf-tsee/o/images%2Fsncf-bg.svg?alt=media");
  background-repeat: no-repeat;
  background-position: 50% 100%;
  z-index: -10;
}

.ar-button-container {
  width: fit-content;
}

ar-button {
  width: calc(100vw - (100vw * 10) / 100);
  height: 3.125rem;
  background-color: var(--sncf-blue);
  border-radius: 32px;
  font-family: "Trueno";
}

.logo {
  width: 5rem;
  height: 5rem;
  margin: 2.5rem 0 3.45rem 1.875rem;
}

ul {
  width: 80vw;
}