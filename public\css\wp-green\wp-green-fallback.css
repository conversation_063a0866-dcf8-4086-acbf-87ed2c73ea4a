:root {
  --ar-icon-size: 1.56rem;
  --kawasaki-green: #6ABE28;
  --ar-button-hover: #42625F;
}

@font-face {
  font-family: "Poppins";
  src: url(/fonts/poppins/Poppins-SemiBold.ttf);
  font-weight: 600;
}

@font-face {
  font-family: "Trueno";
  src: url(/fonts/trueno/TruenoLt.ttf) format("truetype");
  font-weight: 400;
}

@font-face {
  font-family: "Trueno";
  src: url(/fonts/trueno/TruenoSBd.ttf) format("truetype");
  font-weight: 600;
}

* {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  color: white;
}

html {
  height: 100vh;
  height: 100dvh;
  width: 100%;
  background-color: #FFF;
}

body {
  margin: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  min-height:100%;
}

.content-mobile {
  min-height: 90vh;
  flex: 1 0 auto;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.logo-container {
  width: 100vw;
}

.logo {
  width: 33%;
  height: auto;
  margin: 2rem 0 5vh 2rem;
  margin: 2rem 0 5dvh 2rem;
}

a {
  text-decoration: none;
}

.viewer-container {
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin-bottom: 10vh;
  margin-bottom: 10dvh;
  width: 100%;
}

.button {
  position: absolute;
  width: 16rem;
  height: 3.125rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--kawasaki-green);
  border-radius: 50px;
  border: solid 3px var(--kawasaki-green);
  background-color: white;
  font-family: "Poppins";
  font-weight: 600;
  font-size: 1rem;
  bottom: -1.563rem;
}

.button:hover {
  background-color: var(--ar-button-hover);
}

.sketchfab-embed-wrapper {
  width: 100%;
  padding: 1vh 0;
  background-color: #fff;
  margin-top: 5vh;
  border-top: solid 1px #626F65;
  border-bottom: solid 1px #626F65;
}

.sketchfab-embed-wrapper iframe {
  width: 100%;
  min-height: 300px;
  height:45vh;
}

h1 {
  font-family: "Trueno";
  color: var(--kawasaki-green);
  font-size: 1.625rem;
  font-weight: 400;
  line-height: 1.75rem;
  margin-bottom: 1rem;
}

p {
  font-family: "Trueno";
  color: #000;
  font-size: 0.875rem;
  font-weight: 400;
  letter-spacing: 0.32px;
  line-height: 1.125rem;
  text-align: center;
  text-transform: none;
  margin: 0 2rem;
}
