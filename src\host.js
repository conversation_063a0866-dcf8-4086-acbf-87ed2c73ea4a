// @ts-check

import os from 'node:os';

/**
 * @returns {string | undefined}
 */
function getLocalIp() {
  const interfaces = os.networkInterfaces();
  const addresses = [];

  Object.keys(interfaces).forEach((k) => {
    // @ts-ignore
    Object.keys(interfaces[k]).forEach((k2) => {
      const address = interfaces[k]?.[k2];
      if (address.family === 'IPv4' && !address.internal) {
        addresses.push(address.address);
      }
    });
  });

  return addresses.at(0);
}

/**
 * @returns {string}
 */
export function resolveHostname() {
  if (process.env.HOSTNAME) {
    return `https://${process.env.HOSTNAME}`;
  }
  if (getLocalIp()) {
    return `http://${getLocalIp()}:${process.env.PORT}`;
  }
  return `http://127.0.0.1:${process.env.PORT}`;
}
