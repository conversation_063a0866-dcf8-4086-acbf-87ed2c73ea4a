:root {
  --ar-button-color: #ED7210;
  --ar-button-hover: #b2560a;
}

@font-face {
  font-family: "Poppins";
  src: url(/fonts/poppins/Poppins-Regular.ttf);
  font-weight: 400;
}

@font-face {
  font-family: "Poppins";
  src: url(/fonts/poppins/Poppins-SemiBold.ttf);
  font-weight: 600;
}

@font-face {
  font-family: "Poppins";
  src: url(/fonts/poppins/Poppins-Medium.ttf);
  font-weight: 500;
}

body {
  font-family: 'Poppins', sans-serif;
}

.content-mobile {
  justify-content: space-between;
}

.logo-container {
  padding: 3rem;
}

.name-container {
  background-color: var(--ar-button-color);
  color: #fff;
  min-height: 3.5rem;
  box-shadow: 0px 4px 4px 0px rgba(51, 51, 51, 0.2);
  text-align: center;
}

.main-container {
  padding: 1.875rem;
  padding-bottom: 3rem;
}

.headline {
  font-weight: 600;
}

.instruction-container {
  font-weight: 400;
  text-align: center;
}

.ar-button-container {
  margin: 0;
  width: 100%;
}

ar-button {
  height: 2.375rem;
  border-radius: 32px;
  border: none;
  background-color: var(--ar-button-color);
  font-weight: 500;
}