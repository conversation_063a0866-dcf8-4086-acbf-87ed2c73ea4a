# Sentry Integration

This project integrates [Sentry](https://sentry.io/) to monitor and track application errors and performance issues in production environment.

## Installation

Sentry is installed via CDN in the `/views/partials/scripts.ejs` file:

```html
<script
  src="https://js-de.sentry-cdn.com/a637fe611f9b20b5de7f7a07c14660cf.min.js"
  crossorigin="anonymous"
></script>
```

## Environment Variables

Set the following environment variable in your `.env` or runtime environment:

```env
SENTRY_DSN=<your_sentry_dsn>
```

## Configuration

Sentry is initialized in the same file using the environment variable `SENTRY_DSN` to configure the DSN.

The environment variable `ENVIRONMENT` is used to configure the environment.

```html
<script>
  window.sentryOnLoad = function () {
    Sentry.init({
      dsn: "process.env.SENTRY_DSN",
      environment: "process.env.ENVIRONMENT",
    });
  };
</script>
```

