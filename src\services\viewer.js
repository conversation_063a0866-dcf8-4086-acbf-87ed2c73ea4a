/* eslint-disable dot-notation */

import ejs from "ejs";
import { getProduct } from "../database.js";
import getUserLanguage from "../utils/user-language.js";
import ARButtonTranslations from "../i18n/model-viewer.js";
import IntlProvider from "./IntlProvider.js";
import ProductLocale from "../models/ProductLocale.js";
import routes from "../routes.js";

export default async function viewer(req, res) {
  const product = await getProduct(req.uid);

  if (
    !product ||
    !product.organization ||
    product.organization.status === "inactive"
  ) {
    return res.callNotFound();
  }

  const host = `${req.protocol}://${req.hostname}`;
  const arDirectLink = encodeURIComponent(
    `${host}/${routes.ar}/${product.uid}`,
  );
  const { ar } = req.query;
  const { isMobile } = req.userAgent;

  /** @type	{string} */
  const acceptLang = req.query["lang"] || req.headers["accept-language"];

  const intl = new IntlProvider({
    browserAcceptLanguage: acceptLang,
    defaultLanguage: product.defaultLang,
  });

  /** @type {ProductLocale} */
  const locales = intl.findBestOr(
    product.locales,
    ProductLocale.default(),
  );

  const translations = getUserLanguage(req, ARButtonTranslations);

  const props = {
    translations,
    product,
    locales,
    ar,
    arDirectLink,
    isMobile,
  };

  let html;

  if (product.sketchfabModelId) {
    html = await ejs.renderFile("./views/sketchfab-viewer.ejs", props);
  } else {
    html = await ejs.renderFile("./views/model-viewer.ejs", props);
  }

  return res.type("text/html").send(html);
}
