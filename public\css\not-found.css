:root {
  --ar-icon-size: 1.75rem;
}

@font-face {
  font-family: "PulpDisplay";
  src: url(../fonts/PulpDisplay/PulpDisplay-Regular.ttf) format("truetype");
  font-weight: 400;
}

@font-face {
  font-family: "PulpDisplay";
  src: url(../fonts/PulpDisplay/PulpDisplay-Bold.ttf) format("truetype");
  font-weight: 700;
}

* {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  color: white;
}

html {
  height: 100%;
  width: 100%;
  background-color: #fcfcfc;
}

body {
  min-height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
}

.main-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  padding-bottom: 5rem;
}

.text-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  max-width: min-content;
}

.title-container h1 {
  color: #1a1d1f;
  font-size: 3.375rem;
  font-weight: bold;
  font-family: "PulpDisplay", sans-serif;
  font-weight: 700;
  line-height: normal;
  text-wrap: nowrap;
}

.paragraph-container p {
  font-size: 1.075rem;
  font-family: "PulpDisplay", sans-serif;
  font-weight: 400;
  color: #93999e;
  line-height: normal;
}

.image-container {
  max-width: 90%;
}

.image-container img {
  width: 100%;
}

@media screen and (min-width: 1250px) {
  .main-container {
    flex-direction: row;
    padding-bottom: 0;
  }

  .image-container {
    max-width: 40%;
  }

  .text-container {
    gap: 1.2rem;
    max-width: 30%;
    align-items: flex-start;
  }

  .title-container h1 {
    font-size: 4.375rem;
  }

  .paragraph-container p {
    font-size: 1.875rem;
  }
}
