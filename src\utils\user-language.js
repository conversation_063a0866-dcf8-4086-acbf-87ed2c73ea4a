/**
 * @param {import('fastify').FastifyRequest} request
 * @param {object} translations
 * @returns {object}
 */
export default function getUserLanguage(request, translations) {
  const { headers } = request;
  const acceptedLanguages = headers["accept-language"];
  let selectedLanguage = "en"; // Default to English if no match

  if (acceptedLanguages) {
    const languagePriorities = acceptedLanguages.split(",").map((lang) =>
      lang.trim().split(";")[0]
    );
    
    for (let i = 0; i < languagePriorities.length; i += 1) {
      const lang = languagePriorities[i];
      if (translations[lang]) {
        selectedLanguage = lang;
        break;
      }
    }
  }

  return translations[selectedLanguage];
}
