// @ts-check

import TemplateLocale from './TemplateLocale.js';

export default class Template {
  /**
   * @param {Object} config
   * @param {string} config.name
   * @param {Array<TemplateLocale>} config.locales
   */
  constructor({ name, locales }) {
    this.name = name;
    this.locales = locales;
  }

  /**
   * @param {*} data
   * @returns {Template | undefined}
   */
  static from(data) {
    return data
      ? new Template({
        name: data.name,
        locales: data.locales.map((l) => TemplateLocale.from(l)),
      })
      : undefined;
  }
}
