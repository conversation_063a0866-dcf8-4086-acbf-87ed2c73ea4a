import Umami from "../../vendors/Umami.js";
import { getProduct } from "../database.js";
import { base64URLDecode } from "../utils/base64URL.js";

export default async function arRedirect(req, res) {
  const { data } = req.params;
  let parsed;

  try {
    const decoded = base64URLDecode(data);
    parsed = JSON.parse(decoded);
  } catch (error) {
    return res.callNotFound();
  }

  if (!parsed.uid || !parsed.link) {
    return res.callNotFound();
  }

  const product = await getProduct(parsed.uid);

  if (
    !product ||
    !product.organization ||
    product.organization.status === "inactive"
  ) {
    return res.callNotFound();
  }

  const umami = new Umami({
    hostUrl: process.env.UMAMI_HOST,
    websiteId: product.organization.analyticsId,
    userAgent: req.userAgent,
  });

  umami.trackEvent("ar-button", {
    name: product.name,
    code: product.code,
  });

  return res.redirect(parsed.link);
}
