:root {
  --ar-icon-size: 1.75rem;
  --quality-spa-dark-blue: #1979C2;
  --quality-spa-grey: #686777;
  --ar-button-dark-hover: #0E436B;
}

@font-face {
  font-family: "Poppins";
  src: url(/fonts/poppins/Poppins-SemiBold.ttf);
  font-weight: 600;
}

@font-face {
  font-family: "SourceSans3";
  src: url(/fonts/sourcesans/SourceSans3-Bold.ttf) format("truetype");
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "SourceSans3";
  src: url(/fonts/sourcesans/SourceSans3-Regular.ttf) format("truetype");
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

body {
  text-align: center;
}

.content-mobile {
  padding-top: 3.125rem;
  padding-bottom: .5rem;
}

.button {
  border-radius: 32px;
  border: 3px solid rgba(255, 255, 255, 0.8);
  background-color: var(--quality-spa-dark-blue);
  margin-bottom: .5rem;
}

.button:hover {
  background-color: var(--ar-button-dark-hover);
}

.sketchfab-embed-wrapper {
  margin-top: 1.8rem;
  margin-bottom: 1.25rem;
}

.sketchfab-embed-wrapper iframe {
  background-color: #505050;
}

h1 {
  font-family: SourceSans3, sans-serif;
  color: var(--quality-spa-dark-blue);
  font-size: 1.25rem;
  line-height: 1.75rem;
  margin-bottom: 0.8rem;
}

p {
  font-family: SourceSans3, sans-serif;
  color: var(--quality-spa-grey);
  font-size: 0.9rem;
  font-weight: 400;
  line-height: 1.3rem;
  text-align: center;
  text-transform: none;
  margin: 0 15%;
  padding-bottom: 1vh;
}