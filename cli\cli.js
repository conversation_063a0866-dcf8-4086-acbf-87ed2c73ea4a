/* eslint-disable import/no-extraneous-dependencies */
import inquirer from "inquirer";
import createTemplate from "./main.js";

export default async function cli() {
  const questions = [
    {
      type: "input",
      name: "templateName",
      message: "Please enter the name of the template",
    },
    {
      type: "list",
      name: "templateTheme",
      message: "Please choose which theme to use",
      choices: ["dark", "light"],
      default: "dark",
    },
  ];

  const answers = await inquirer.prompt(questions);

  const templateName = answers.templateName
    .normalize("NFD")
    .replaceAll(/[\u0300-\u036f]/g, "")
    .replaceAll(" ", "-")
    .toLowerCase();

  const options = {
    templateDirectory: "cli/_basicTemplates",
    templateTargetDirectory: `views/templates/${templateName}`,
    cssDirectory: "cli/_basicCSS",
    cssTargetDirectory: `public/css/${templateName}`,
    templateTheme: answers.templateTheme,
    templateName,
  };

  await createTemplate(options);
}

cli();
