* {
  margin: 0;
  padding: 0;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}

span {
  color: inherit;
  font-size: inherit;
  font-family: inherit;
  font-weight: inherit;
}

video {
  -webkit-mask-image: -webkit-radial-gradient(white, black);
  mask-image: radial-gradient(white, black);
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  -moz-backface-visibility: hidden;
}

.hidden {
  display: none !important;
}

/* ---------- <PERSON><PERSON><PERSON> set up ---------- */

@font-face {
  font-family: "fontello";
  src: url(../fonts/fontello/fontello.ttf) format("truetype");
  font-weight: normal;
  font-style: normal;
}

[class^="icon-"]:before,
[class*=" icon-"]:before {
  font-family: "fontello";
  font-style: normal;
  font-weight: normal;

  display: inline-block;
  text-decoration: inherit;
  width: auto;
  margin-right: 0.5rem;
  text-align: center;

  /* For safety - reset parent styles, that can break glyph codes*/
  font-variant: normal;
  text-transform: none;

  /* you can be more comfortable with increased icons size */
  font-size: 150%;
  line-height: 100%;

  /* Font smoothing. That was taken from TWBS */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-ar:before {
  content: "\e800"; /* -> AR icon */
}

/* ----------------- Generic style ----------------- */

html {
  height: 100%;
  width: 100%;
  background-color: #fff;
}

body {
  position: relative;
  margin: 0;
  font-size: 0.875rem;
  line-height: 1.25rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  min-height: 100vh;
  min-height: 100svh;
  height: 100%;
  padding: 0;
}

.content-mobile {
  position: relative;
  flex: 0 0 auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  min-height: 100vh;
  min-height: 100svh;
  height: auto;
}

.logo-container {
  width: 100%;
  display: flex;
  justify-content: flex-start;
}

.logo {
  width: auto;
}

.main-container {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-evenly;
  position: relative;
}

.name-container {
  color: #000;
  width: 100vw;
  font-weight: 600;
  font-size: 1.125rem;
  line-height: 1.7rem;
  display: flex;
  justify-content: center;
  align-items: center;
}

.headline {
  width: 100%;
  font-weight: 300;
  line-height: 120%;
  font-size: 1.313rem;
  margin-bottom: 1rem;
}

.video-container {
  max-width: 100vw;
}

.video-tuto {
  max-width: 100%;
}

.head-container {
  display: flex;
  padding: 0 2rem;
  gap: 1.25rem;
}

.viewer-container {
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
}

.sketchfab-embed-wrapper {
  width: 100%;
  background-color: #fff;
  margin-bottom: 0.5rem;
}

.sketchfab-embed-wrapper iframe {
  width: 100%;
  min-height: 300px;
  height: 45vh;
}

.sketchfab-embed-wrapper model-viewer {
  width: 100%;
  height: 100%;
}

.instruction-container {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.instruction-title {
  width: 100%;
  font-size: 0.875rem;
  line-height: 1.125rem;
  font-weight: 400;
  letter-spacing: 0.02rem;
}

.text-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  font-size: 0.813rem;
  font-weight: 600;
}

p {
  font-size: 0.8rem;
  font-weight: 300;
  letter-spacing: 0.32px;
  line-height: 1.125rem;
  text-transform: none;
}

ul {
  width: 100%;
}

ul p {
  margin-bottom: 0.3rem;
  font-family: SourceSans3, sans-serif;
  font-size: 0.9rem;
  line-height: 1.5rem;
  font-weight: 700;
  letter-spacing: 0.02rem;
  text-transform: none;
}

a {
  text-decoration: none;
}

.ar-button-container {
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
  z-index: 10;
}

ar-button {
  width: 100%;
  height: 3.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--ar-button-color);
  font-family: "Poppins";
  font-weight: 600;
  font-size: 1rem;
  color: #fff;
}

.button {
  width: 16rem;
  height: 3.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--ar-button-color);
  font-family: "Poppins";
  font-weight: 600;
  font-size: 1rem;
  margin-bottom: 1.5rem;
  color: #fff;
}

.watermark {
  position: absolute;
  bottom: 0;
}
