:root {
  --ar-icon-size: 1.75rem;
  --wonder-blue: #0094aa;
}

@font-face {
  font-family: "Poppins";
  src: url(/fonts/poppins/Poppins-SemiBold.ttf);
  font-weight: 600;
}

@font-face {
  font-family: "Trueno";
  src: url(/fonts/trueno/TruenoLt.ttf) format("truetype");
  font-weight: 400;
}

@font-face {
  font-family: "Trueno";
  src: url(/fonts/trueno/TruenoSBd.ttf) format("truetype");
  font-weight: 600;
}

* {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  color: white;
}

html {
  height: 100%;
  width: 100%;
  background-color: var(--wonder-blue);
}

body {
  margin: 0;
  font-size: 0.9rem;
  line-height: 1.25rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100vh;
  height: 100dvh;
  padding: 0;
}

.content-mobile {
  min-height: 70vh;
  margin-top: 20vh;
  margin-top: 20dvh;
  width: 100vw;
  flex: 0 0 auto;
  display: flex;
  flex-direction: column;
}

.headline {
  width: 80vw;
  font-family: "Trueno";
  font-weight: 600;
  color: #fff;
  margin-top: 5.6rem;
  margin-bottom: 1rem;
}

.instruction-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  font-family: "Trueno";
  font-size: 14px;
}

.main-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: calc((100vh * 10 / 100) - 1rem);
  padding-bottom: 1rem;
  position: relative;
}

.ar-button-container {
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
}

.main-container:before {
  content: " ";
  display: block;
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  opacity: 0.2;
  background-image: url("https://firebasestorage.googleapis.com/v0/b/augmented-shop-44400.appspot.com/o/images%2Fwonder-pattern.svg?alt=media");
  background-repeat: no-repeat;
  background-position: 50% 0;
  background-size: cover;
  z-index: -10;
}

.ar-button-container {
  margin: 0 auto;
  width: fit-content;
}

ar-button {
  width: calc(100vw - (100vw * 10) / 100);
  height: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--wonder-blue);
  background-color: white;
  border-radius: 32px;
  font-family: "Trueno";
  font-weight: 600;
  font-size: 1rem;
}

ul {
  width: 80vw;
}

.watermark {
  position: absolute;
  bottom: 0.3rem;
}

