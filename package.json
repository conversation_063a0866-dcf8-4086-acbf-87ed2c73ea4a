{"name": "ar-services", "version": "0.0.1", "volta": {"node": "18.14.0"}, "type": "module", "scripts": {"lint": "npx eslint .", "start": "node src/server.js", "dev:staging": "cross-env DOTENV_CONFIG_PATH='.env.staging' nodemon -r dotenv/config --inspect src/server.js", "dev:prod": "cross-env DOTENV_CONFIG_PATH='.env.prod' nodemon -r dotenv/config --inspect src/server.js", "create": "node cli/cli.js"}, "dependencies": {"@fastify/static": "^6.10.0", "@fastify/view": "^7.4.1", "dotenv": "^16.0.3", "ejs": "^3.1.9", "fastify": "^4.12.0", "fastify-user-agent": "^1.0.1", "postgres": "^3.3.4"}, "devDependencies": {"chalk": "^5.3.0", "cross-env": "^7.0.3", "eslint": "^8.48.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-editorconfig": "^4.0.2", "eslint-plugin-import": "^2.27.5", "fs": "^0.0.1-security", "inquirer": "^9.2.12", "listr": "^0.14.3", "ncp": "^2.0.0", "nodemon": "^2.0.20", "path": "^0.12.7", "util": "^0.12.5"}}