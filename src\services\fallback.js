// @ts-check

import IntlProvider from "./IntlProvider.js";
import { getProduct, getTemplateOrDefault } from "../database.js";
import ProductLocale from "../models/ProductLocale.js";
import TemplateLocale from "../models/TemplateLocale.js";
import getViewOrDefault from "../utils/views.js";

export default async function fallback(req, res) {
  const product = await getProduct(req.uid);

  if (
    !product ||
    !product.organization ||
    product.organization.status === "inactive"
  ) {
    return res.callNotFound();
  }

  const template = await getTemplateOrDefault(product.organization.templateId);

  if (!template) return res.callNotFound();

  const intl = new IntlProvider({
    browserAcceptLanguage: req.headers["accept-language"],
    defaultLanguage: product.defaultLang,
  });

  /** @type {ProductLocale} */
  const productLocale = intl.findBestOr(
    product.locales,
    ProductLocale.default(),
  );

  /** @type {TemplateLocale} */
  const templateLocale = intl.findBestOr(
    template.locales,
    TemplateLocale.default(),
  );

  const viewProps = {
    name: product.name,
    modelName: product.modelName,
    orgName: product.organization.name,
    bucket: product.organization.bucket,
    logo: product.organization.logoUrl,
    favicon: product.organization.faviconUrl,
    webclip: product.organization.webclipUrl,
    title: productLocale.title,
    fallbackTitle: templateLocale.fallbackTitle,
    fallbackSubtitle: templateLocale.fallbackSubtitle,
    fallbackViewer: templateLocale.fallbackViewer,
    sketchfabModelId: product.sketchfabModelId,
    link: productLocale.link,
    buttonName: productLocale.callToAction,
  };

  return res.view(
    await getViewOrDefault("fallbacks", template.name),
    viewProps,
  );
}
