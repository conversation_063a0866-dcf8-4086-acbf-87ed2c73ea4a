<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Wonder Shop - <%= title %>
  </title>
  <link rel="stylesheet" href="/css/style.css" />
  <link rel="stylesheet" href="/css/weeride/weeride-fallback.css" />

  <% switch (orgName) {
    case "Animal Valley" : %>
    <link rel="stylesheet" href="/css/weeride/weeride-av.css" />
  <% break;
    case "Niche a chien" : %>
    <link rel="stylesheet" href="/css/weeride/weeride-nac.css" />
  <% break;
    case "Chemin des poulaillers" : %>
    <link rel="stylesheet" href="/css/weeride/weeride-cdp.css" />
  <% break;
    case "Poulailler direct" : %>
    <link rel="stylesheet" href="/css/weeride/weeride-pd.css" />
  <% break; 
    default: break;}%>

  <%- include('../../partials/favicon.ejs') %>
  <%- include('../../partials/webclip.ejs') %>
</head>

<body>
  <div class="content-mobile">
    <div class="main-container">
      <div class="text-container">
        <h1>
          <%= fallbackTitle %>
        </h1>
        <div>
          <p>
            <%= fallbackSubtitle %>
          </p>
          <p>
            <%= fallbackViewer %>
          </p>
        </div>
      </div>
      <div class="viewer-container">
        <% if (locals.sketchfabModelId) { %>
          <%- include('../../partials/sketchfab-viewer.ejs') %>
        <% } %>

        <% if (locals.link) { %>
          <a class="button" href="<%= link %>">
            <%= buttonName %>
          </a>
        <% } %>
      </div>

      <div class="watermark">
        <%- include('../../partials/powered-by-wp.ejs', {theme: 'dark' }) %>
      </div>
    </div>
  </div>
</body>

</html>