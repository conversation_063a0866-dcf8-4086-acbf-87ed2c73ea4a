:root {
  --ar-icon-size: 1.75rem;
  --ar-button-color: #27aec2;
}

@font-face {
  font-family: "Helvetica";
  src: url(/fonts/helvetica/HelveticaNeue.ttf) format("truetype");
  font-weight: 600;
}

@font-face {
  font-family: "Poppins";
  src: url(/fonts/poppins/Poppins-Light.ttf);
  font-weight: 300;
}

@font-face {
  font-family: "DINPro";
  src: url(/fonts/dinpro/DINPro-Bold.otf) format("truetype");
  font-weight: 700;
}

@font-face {
  font-family: "DINPro";
  src: url(/fonts/dinpro/DINPro.otf) format("truetype");
  font-weight: 400;
}

@font-face {
  font-family: "DINPro";
  src: url(/fonts/dinpro/DINPro-Medium.otf) format("truetype");
  font-weight: 500;
}

@font-face {
  font-family: "Open Sans";
  src: url(/fonts/opensans/OpenSans-Medium.ttf) format("truetype");
  font-weight: 600;
}

body {
  font-size: 0.9rem;
  line-height: 1.25rem;
}

.logo {
  width: 10rem;
  margin: 8vh 0 8vh 1.875rem;
}

.header-color-container {
  width: 100%;
  min-height: 5px;
  display: flex;
}

.header-color {
  width: 20%;
}

.header-color-purple {
  background-color: #95518a;
}
.header-color-blue {
  background-color: #63b8d6;
}
.header-color-green {
  background-color: #bed459;
}
.header-color-yellow {
  background-color: #fcc256;
}
.header-color-pink {
  background-color: #dd538c;
}

.name-container {
  background-color: #27aec2;
  color: #fff;
  box-shadow: 0px 4px 4px 0px rgba(51, 51, 51, 0.2);
  font-family: "DINPro";
  padding: 1.65vh 1.875rem;
  text-align: center;
}

.headline {
  font-family: "DINPro";
  font-weight: 700;
  color: #000;
  font-size: 1.625rem;
}

.instruction-container {
  padding-left: 1.875rem;
  padding-right: 1.875rem;
  margin-bottom: 2.5rem;
}

.main-container {
  padding-top: 3.5rem;
}

.texts-container {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.main-container-bundle {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4.1rem;
  padding-top: 3.5rem;
  position: relative;
}

.main-container h2,
.main-container-bundle h2 {
  padding-left: 1.875rem;
  padding-right: 1.875rem;
}

.bottom-container,
.bundle-bottom-container {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-top: 5vh;
  padding-bottom: 1rem;
  position: relative;
  min-height: 25vh;
  min-height: 25dvh;
  flex: 0 1 0;
}

.bundle-bottom-container {
  flex: 0 1 auto;
}

.bottom-container:before,
.bundle-bottom-container:before {
  content: " ";
  display: block;
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  opacity: 0.25;
  background-image: url("https://firebasestorage.googleapis.com/v0/b/intex-ws/o/images%2Ffond-texture-eau.webp?alt=media");
  background-repeat: no-repeat;
  background-position: 50% 0;
  background-size: cover;
  z-index: -10;
}

.ar-button-container,
.bundle-ar-button-container {
  margin: 0 auto ;
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
  width: fit-content;
}

.bundle-bottom {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  flex: 1 1 auto;
}

.bundle-bottom p {
  margin: 0 0 0.8rem 1.875rem;
  font-family: "DINPro";
  font-weight: 500;
}

.bundle-button-container {
  margin: 0 auto 3rem auto;
  display: flex;
  flex-direction: column;
  gap: 1.875rem;
  width: fit-content;
}

.ar-button-container ar-button {
  width: calc(100vw - (100vw * 10) / 100);
  height: 2.625rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  background-color: var(--ar-button-color);
  border: 3px solid rgba(255, 255, 255, 0.4);
  border-radius: 50px;
  font-family: "Open Sans";
  font-weight: 600;
  font-size: 1rem;
}

.bundle-ar-button-container ar-button {
  width: calc(100vw - (100vw * 10) / 100);
  height: 2.625rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  background-color: var(--ar-button-color);
  border: 3px solid rgba(255, 255, 255, 0.4);
  border-radius: 50px;
  font-family: "Open Sans";
  font-weight: 600;
  font-size: 1rem;
}

.bundle-button-container ar-button {
  width: calc(100vw - (100vw * 10) / 100);
  height: 2.625rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  background-color: #0d4a5e;
  border-radius: 50px;
  font-family: "Open Sans";
  font-weight: 600;
  font-size: 1rem;
}

ul p {
  width: 100%;
  font-size: 0.875rem;
  line-height: 1.125rem;
  font-family: "DINPro";
  font-weight: 400;
  color: #000;
}
