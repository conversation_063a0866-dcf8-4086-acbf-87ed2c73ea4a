:root {
  --ar-icon-size: 1.75rem;
  --chazelles-orange : #F17233;
  --ar-button-hover : #E14222;
}

@font-face {
  font-family: "Poppins";
  src: url(/fonts/poppins/Poppins-SemiBold.ttf);
  font-weight: 600;
}
@font-face {
  font-family: 'SourceSans3';
  src: url(/fonts/sourcesans/SourceSans3-Bold.ttf) format('truetype');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

* {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  color : white;
}
html {
  height: 100%;
  width: 100%;
  background-color: #706962;
}
body {
  margin: 0;
  font-size: 0.9rem;
  line-height: 1.25rem;
  display: flex;
  flex-direction: column;
  align-items: left;
  justify-content: space-around;
  height: 100vh;
  height: 100dvh;
  padding: 0 2rem;
}
.headline {
  width: 75%;
  font-family: "Poppins";
  font-weight: 600;
  font-size: 1.9rem;
  line-height: 2.3rem;
}
a {
  text-decoration: none;
}
.logo-container {
  margin: 0 auto;
  height: 10rem;
  max-width: 100%;
  object-fit: contain;
  align-items: center;
}
.logo{
  max-height: 100%;
  max-width: 100%;
}

.ar-button-container {
  margin: 0 auto;
}

ar-button {
  width: 16rem;
  height: 3.5rem;

  display: flex;
  align-items: center;
  justify-content: center;

  border-radius: 32px;
  border: solid 3px rgba(255, 255, 255, 0.8);
  background-color: var(--chazelles-orange);
  font-family: "Poppins";
  font-weight: 600;
  font-size: 1rem;
}

ar-button:hover {
  background-color: var(--ar-button-hover);
}

.instruction-title{
  width: 12rem;
  padding-bottom: 0.5rem;
  border-style: none none solid;
  border-width: 1px;
  border-color: #000 #000 var(--chazelles-orange);
  font-family: SourceSans3, sans-serif;
  font-size: 1.25rem;
  line-height: 1.5rem;
  font-weight: 700;
  text-align: left;
  letter-spacing: 0.02rem;
  margin-bottom:0.6rem;
}

ul p{
  margin-bottom: 0.3rem;
  font-family: SourceSans3, sans-serif;
  font-size: 1rem;
  line-height: 1.6rem;
  font-weight: 700;
  letter-spacing: 0.02rem;
  text-transform: none;
}
