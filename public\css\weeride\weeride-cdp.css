:root {
  --ar-button-color: #e28c4e;
  --ar-button-hover: #b8845f;
  --background-color: #ffff;
  --font-header: "Coniferous";
  --font-body: "GolosText";
}

@font-face {
  font-family: "GolosText";
  src: url(/fonts/GolosText/GolosText-VariableFont_wght.ttf) format("truetype");
  font-weight: 400;
  font-style: normal;
}

@font-face {
  font-family: "Coniferous";
  src: url(/fonts/Coniferous/Coniferous.ttf) format("truetype");
  font-weight: 400;
  font-style: normal;
}

.logo {
  width: 100%;
}

.logo-container {
  max-width: 50%;
}

.logo-desktop {
  width: 100% !important;
  max-width: 350px !important;
  max-height: 150px !important;
  height: auto !important;
  margin: 3rem 12vw !important;
  padding: 0% !important;
}

.logo-container-desktop {
  width: 100%;
  max-height: 300px;
  background: white !important;
}

.name-container-desktop {
  background-color: transparent !important;
  font-family: var(--font-header) !important;
}

.headline-desktop {
  font-family: var(--font-body), sans-serif !important;
}
