<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Wonder Shop - <%= title %>
  </title>
  <link rel="stylesheet" href="/css/style.css" />
  <link rel="stylesheet" href="/css/quality-spa/quality-spa.css" />

    <%- include('../../partials/scripts.ejs') %>
  <%- include('../../partials/favicon.ejs') %>
  <%- include('../../partials/webclip.ejs') %>
</head>

<body>
  <div class="content-mobile">
    <div class="logo-container">
      <img class="logo" src="<%= logo %>" alt="logo">
    </div>
    <div class="main-container">
      <h2 class="headline">
        <%= headline %>
      </h2>
      <div class="instruction-container">
        <h3 class="instruction-title">
          <%= instructionTitle %>
        </h3>
        <div class="ar-button-container">
          <%- include('../../partials/ar-button.ejs') %>
        </div>
        <div class="viewer-button">
          <button id="viewerBtn">
            Voir le mode nuit
          </button>
        </div>
      </div>
    </div>
    <div id="viewer-section" class="d-none">
      <div class="main-container">
        <div class="head-section">

          <h2 class="viewer-headline">
            Mode nuit
          </h2>
          <div id="closeBtn">
            <svg width="100%" height="100%" viewBox="0 0 50 50" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xml:space="preserve" xmlns:serif="http://www.serif.com/" style="fill-rule:evenodd;clip-rule:evenodd;stroke-linejoin:round;stroke-miterlimit:2;">
              <rect id="Plan-de-travail1" serif:id="Plan de travail1" x="0" y="0" width="50" height="50" style="fill:none;" />
              <g id="Plan-de-travail11" serif:id="Plan de travail1">
                <g transform="matrix(2.64426,0,0,2.65323,-6.73107,-6.83873)">
                  <path d="M13.414,12L20.485,19.071L19.071,20.485L12,13.414L4.929,20.485L3.515,19.071L10.586,12L3.515,4.929L4.929,3.515L12,10.586L19.071,3.515L20.485,4.929L13.414,12Z" style="fill:rgb(80,80,80);fill-rule:nonzero;" />
                </g>
              </g>
            </svg>
          </div>

        </div>
        <div class="instruction-container">
          <h3 class="instruction-title">
            Découvrez le Spa en mode nuit dans le viewer 3D ci-dessous :
          </h3>
        </div>
      </div>
      <div class="sketchfab-embed-wrapper">
        <iframe title="<%= name %>" frameborder="0" allowfullscreen mozallowfullscreen="true" webkitallowfullscreen="true" allow="autoplay; fullscreen; xr-spatial-tracking" xr-spatial-tracking execution-while-out-of-viewport execution-while-not-rendered web-share src="https://sketchfab.com/models/<%= sketchfabModelId %>/embed?autostart=1&camera=0&ui_animations=0&ui_infos=0&ui_stop=0&ui_inspector=0&ui_watermark_link=0&ui_watermark=0&ui_ar=0&ui_help=0&ui_settings=0&ui_vr=0&ui_fullscreen=0&ui_annotations=0">
        </iframe>
      </div>
    </div>
  </div>
  <div class="watermark">
    <%- include('../../partials/powered-by-wp.ejs', {theme: 'dark' }) %>
  </div>

  <script>
    const viewerBtn = document.getElementById("viewerBtn");
    const closeBtn = document.getElementById("closeBtn");
    const viewerSection = document.getElementById("viewer-section");

    viewerBtn.addEventListener("click", () => {
      viewerSection.classList.add('modal-in');
      viewerSection.classList.remove("modal-out");
      viewerSection.classList.remove("d-none");
    })
    closeBtn.addEventListener("click", () => {
      viewerSection.classList.remove('modal-in');
      viewerSection.classList.add("modal-out");
      setTimeout(() => {
        viewerSection.classList.add("d-none");
      }, "500")
    })
  </script>

  <%- include('../../partials/desktop-fallback.ejs', {viewer: true }) %>
</body>

</html>