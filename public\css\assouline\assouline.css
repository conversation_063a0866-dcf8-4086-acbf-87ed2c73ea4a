:root {
  --ar-icon-size: 1.75rem;
  --font-color: #FFF;
}

@font-face {
  font-family: "Gotham";
  src: url(/fonts/gotham/Gotham-Regular.ttf) format("truetype");
  font-weight: 400;
}

@font-face {
  font-family: "Gotham";
  src: url(/fonts/gotham/Gotham-Light.otf) format("truetype");
  font-weight: 300;
}

@font-face {
  font-family: "Didot";
  src: url(/fonts/didot/Didot-Regular.ttf) format("truetype");
  font-weight: 400;
}

body {
  font-size: 0.9rem;
}

.logo {
  width: 5rem;
  margin: 2.5rem 0 3.45rem 1.875rem;
}

.main-container {
  gap: 4.375rem;
  padding: 3rem 0 1rem 0;
}

.headline {
  font-family: "Didot";
  font-weight: 400;
  color: #000;
  font-size: 1.25rem;
  text-transform: uppercase;
  letter-spacing: -0.125rem,
}

.instruction-container {
  padding-left: 1.875rem;
  padding-right: 1.875rem;
}

.main-container h2 {
  padding-left: 1.875rem;
  padding-right: 1.875rem;
}

.ar-button-container {
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
}

ar-button {
  width: 75vw;
  height: 2.75rem;
  color: #fff;
  background-color: #000;
  font-family: "Gotham";
  font-weight: 350;
  font-size: 0.75rem;
  text-transform: uppercase;
}

ul p {
  width: 100%;
  font-size: 0.875rem;
  font-family: "Gotham";
  font-weight: 400;
  color: #000;
  line-height: 160%;
}
