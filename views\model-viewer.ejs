<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Wonder Shop - <%= product.name %></title>

    <link rel="stylesheet" href="../css/reset.css" />
    <link rel="stylesheet" href="../css/loader.css" />

    <script
      type="module"
      src="https://ajax.googleapis.com/ajax/libs/model-viewer/3.4.0/model-viewer.min.js"
    ></script>

    <% if (locals.product.organization.analyticsId) { %>
    <script
      defer
      src="<%= process.env.UMAMI_HOST + '/script.js' %>"
      data-website-id="<%= product.organization.analyticsId %>"
    ></script>
    <% } %>

    <script
      type="module"
      src="https://unpkg.com/@wonder-partners/ar-button/main.min.js"
    ></script>
  </head>

  <style>
    @font-face {
      font-family: "PulpDisplay";
      src: url(/fonts/PulpDisplay/PulpDisplay-Regular.ttf) format("truetype");
      font-weight: 400;
    }

    @font-face {
      font-family: "PulpDisplay";
      src: url(/fonts/PulpDisplay/PulpDisplay-Bold.ttf) format("truetype");
      font-weight: 700;
    }

    body {
      overflow: hidden;
      font-family: "PulpDisplay", sans-serif;
    }

    button {
      display: flex;
      align-items: center;
      font-size: 1rem;
      font-family: "PulpDisplay", sans-serif;
      cursor: pointer;
      border: none;
      outline: none;
    }

    .pill {
      padding: 0.5rem 1rem;
      border-radius: 30rem;
      background-color: white;
      box-shadow: 0 0 1em rgb(0 0 0 / 0.1);
    }

    .pill:hover {
      background-color: #eee;
    }

    .model-viewer {
      width: 100%;
      height: 100vh;
    }

    .ar-button {
      display: flex;
      align-items: center;
      position: absolute;
      bottom: 1.5rem;
      right: 1.5rem;
      transition: opacity 0.3s ease;
      overflow: hidden;
    }

    .ar-button.hidden {
      opacity: 0;
      pointer-events: none;
    }

    /* Animation states for AR button reveal */
    .ar-button.expanding {
      width: 3rem;
      height: 3rem;
      border-radius: 50%;
      animation: expandButton 0.8s ease-out forwards;
    }

    .ar-button.expanding span {
      opacity: 0;
      transform: translateX(10px);
      animation: fadeInText 0.4s ease-out 0.4s forwards;
    }

    @keyframes expandButton {
      0% {
        width: 3rem;
        height: 3rem;
        border-radius: 50%;
      }
      100% {
        width: auto;
        height: auto;
        border-radius: 30rem;
      }
    }

    @keyframes fadeInText {
      0% {
        opacity: 0;
        transform: translateX(10px);
      }
      100% {
        opacity: 1;
        transform: translateX(0);
      }
    }

    .icon {
      width: 2rem;
      height: 2rem;
      margin-right: 0.25rem;
    }

    #modal {
      background-color: transparent;
      max-width: 50ch;
      border: 0;
      color: white;
    }

    #modal > #content {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 1.5rem;
      text-align: center;
    }

    #modal > #content > h1 {
      font-size: 1.25rem;
      font-weight: 700;
      line-height: 1.5rem;
    }

    #modal::backdrop {
      background: rgb(0 0 0 / 0.8);
    }
  </style>

  <body>
    <model-viewer
      id="viewer"
      class="model-viewer"
      alt="<%= product.name %> 3D model"
      src="<%= product.glbUrl %>"
      loading="eager"
      shadow-intensity="1"
      camera-controls
      touch-action="pan-y"
    >
      <div slot="progress-bar" aria-hidden="true">
        <div class="loading-container">
          <div class="loading-percentage" id="loading-percentage">Loading: 0%</div>
          <div class="boxes">
            <div class="box">
              <div></div>
              <div></div>
              <div></div>
              <div></div>
            </div>
            <div class="box">
              <div></div>
              <div></div>
              <div></div>
              <div></div>
            </div>
            <div class="box">
              <div></div>
              <div></div>
              <div></div>
              <div></div>
            </div>
            <div class="box">
              <div></div>
              <div></div>
              <div></div>
              <div></div>
            </div>
          </div>
        </div>
      </div>
    </model-viewer>

    <% if (ar === '1') { %> <% if (isMobile) { %>

    <ar-button
      class="ar-button pill hidden"
      data-product-name="<%= product.name %>"
      data-product-code="<%= product.code %>"
      src="<%= product.glbUrl %>"
      ios-src="<%= product.usdzUrl %>"
      title="<%= locales.title %>"
      occlusion="<%= locales.occlusion %>"
      fallback-url="<%= locales.fallbackUrl %>"
      link="<%= locales.link %>"
      checkout-title="<%= locales.checkoutTitle %>"
      checkout-subtitle="<%= locales.checkoutSubtitle %>"
      call-to-action="<%= locales.callToAction %>"
      ios-link="<%= locales.iosLink || locales.link %>"
      price="<%= locales.price %>"
      canonical-web-page-url="<%= locales.canonicalWebPageUrl %>"
      allows-content-scaling="<%= locales.allowsContentScaling === true ? 1 : 0 %>"
    >
      <img class="icon" src="/images/arkit-glyph_light.webp" alt="ar-glyph" />
      <span><%= translations.label %></span>
    </ar-button>

    <% } else { %>

    <button class="ar-button pill hidden" onclick="openModal()">
      <img
        class="icon"
        src="/images/arkit-glyph_light.webp"
        alt="ar-glyph"
      />
      <span><%= translations.label %></span>
    </button>

    <dialog id="modal">
      <div id="content">
        <h1><%= translations.title %></h1>
        <div>
          <img
            src="https://chart.wonder.partners/qr?text=<%= arDirectLink %>&size=200&format=svg"
            alt="qr"
          >
        </div>
        <button class="pill" onclick="closeModal()">
          ✖ <%= translations.close %>
        </button>
      </div>
    </dialog>

    <% } %> <% } %>

    <script>
      const modal = document.getElementById("modal");

      function openModal() {
        modal.showModal();
      }

      function closeModal() {
        modal.close();
      }

      window.addEventListener("DOMContentLoaded", () => {
        const umami = window.umami;
        const modelViewer = document.getElementById("viewer");
        let viewerInteract = false;

        const productEventPayload = {
          code: "<%= product.code %>",
          name: "<%= product.name %>",
        };

        umami.track("viewer-impression", productEventPayload);

        modelViewer.addEventListener("click", () => {
          if (viewerInteract) return;

          umami.track("viewer-interact", productEventPayload);
          viewerInteract = true;
        });
      });
    </script>

    <script>
      const mv = document.getElementById("viewer");
      const loadingContainer = mv.querySelector(".loading-container");
      const loadingPercentage = document.getElementById("loading-percentage");

      mv.addEventListener("progress", (e) => {
        const progress = e.detail.totalProgress ?? 0;
        const percentage = Math.round(progress * 100);

        // Update percentage display
        loadingPercentage.textContent = `Loading: ${percentage}%`;

        if (progress >= 1) {
          loadingContainer.classList.add("hidden");
        } else {
          loadingContainer.classList.remove("hidden");
        }
      });

      mv.addEventListener("load", () => {
        loadingPercentage.textContent = "Loading: 100%";
        loadingContainer.classList.add("hidden");

        // Show AR button once model viewer has loaded with animation
        const arButton = document.querySelector(".ar-button");
        if (arButton) {
          arButton.classList.remove("hidden");
          arButton.classList.add("expanding");

          // Clean up animation class after animation completes
          setTimeout(() => {
            arButton.classList.remove("expanding");
          }, 800); // Match the animation duration
        }
      });
    </script>
  </body>
</html>
