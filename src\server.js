// @ts-check

import fastify from "fastify";
import * as userAgent from "fastify-user-agent";
import fastifyView from "@fastify/view";
import fastifyStatic from "@fastify/static";
import ejs from "ejs";
import { fileURLToPath } from "node:url";
import { dirname, join } from "node:path";

import { getAgentDetails } from "./utils/user-agent.js";
import { validateUid } from "./middlewares/uid-validation.js";
import { resolveHostname } from "./host.js";
import getUserLanguage from "./utils/user-language.js";
import notFoundTranslations from "./i18n/not-found.js";
import globalError from "./i18n/global-error.js";
import arDirect from "./services/ar-direct.js";
import landing from "./services/landing.js";
import fallback from "./services/fallback.js";
import bundle from "./services/bundle.js";
import viewer from "./services/viewer.js";
import routes from "./routes.js";
import arRedirect from "./services/ar-redirect.js";

const app = fastify({
  trustProxy: true,
  maxParamLength: 1000,
});
const port = Number(process.env.PORT);
const host = "0.0.0.0";
const rootDir = dirname(dirname(fileURLToPath(import.meta.url)));

// @ts-ignore
app.register(userAgent);
app.register(fastifyView, {
  engine: { ejs },
});
app.register(fastifyStatic, {
  root: join(rootDir, "public"),
});
app.addHook("preHandler", getAgentDetails);

app.setNotFoundHandler((req, res) => {
  const translations = getUserLanguage(req, notFoundTranslations);
  res.code(404).view("views/errors/not-found.ejs", { translations });
});

app.setErrorHandler((error, req, res) => {
  const translations = getUserLanguage(req, globalError);
  res.code(500).view("views/errors/global-error.ejs", { translations });
});

app.get("/health", (req, res) => {
  // @ts-ignore
  res.code(200).send("OK");
});

app.get(`/${routes.ar}/:uid`, { preHandler: [validateUid] }, arDirect);
app.get(`/${routes.landing}/:uid`, { preHandler: [validateUid] }, landing);
app.get(`/${routes.fallback}/:uid`, { preHandler: [validateUid] }, fallback);
app.get(`/${routes.bundle}/:uid`, { preHandler: [validateUid] }, bundle);
app.get(`/${routes.viewer}/:uid`, { preHandler: [validateUid] }, viewer);
app.get(`/${routes.arRedirect}/:data`, arRedirect);

app
  .listen({ port, host })
  .then(() => {
    console.log(`Server is running: ${resolveHostname()}`);
  })
  .catch((err) => {
    console.error(err);
    process.exit(1);
  });
