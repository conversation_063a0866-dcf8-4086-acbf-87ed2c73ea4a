:root {
  --ar-icon-size: 1.56rem;
  --button: #27AEC2;
  --button-hover: #1e7480;
}

@font-face {
  font-family: "Poppins";
  src: url(/fonts/poppins/Poppins-SemiBold.ttf);
  font-weight: 600;
}

@font-face {
  font-family: "DINPro";
  src: url(/fonts/dinpro/DINPro-Bold.otf) format("truetype");
  font-weight: 700;
}

@font-face {
  font-family: "DINPro";
  src: url(/fonts/dinpro/DINPro.otf) format("truetype");
  font-weight: 400;
}

@font-face {
  font-family: "Open Sans";
  src: url(/fonts/opensans/OpenSans-Light.ttf) format("truetype");
  font-weight: 300;
}

@font-face {
  font-family: "Open Sans";
  src: url(/fonts/opensans/OpenSans-Regular.ttf) format("truetype");
  font-weight: 400;
}

body {
  font-size: 0.9rem;
}

.header-color-container {
  width: 100%;
  min-height: 5px;
  display: flex;
}

.header-color {
  width: 20%;
}

.header-color-purple {
  background-color: #95518a;
}
.header-color-blue {
  background-color: #63b8d6;
}
.header-color-green {
  background-color: #bed459;
}
.header-color-yellow {
  background-color: #fcc256;
}
.header-color-pink {
  background-color: #dd538c;
}

.logo-container {
  width: 100vw;
}

.logo {
  width: 10rem;
  margin: 2.5rem 0 1.2rem 1.875rem;
}

.viewer-container {
  height: 100%;
  flex: 1;
}

.bottom-container {
  margin: 0 auto;
  min-height: 25vh;
  min-height: 25dvh;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-top: 1rem;
  padding-bottom: 1rem;
  position: relative;
}

.bottom-container:before {
  content: " ";
  display: block;
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  opacity: 0.25;
  background-image: url("https://firebasestorage.googleapis.com/v0/b/intex-ws/o/images%2Ffond-texture-eau.webp?alt=media");
  background-repeat: no-repeat;
  background-position: 50% 0;
  background-size: cover;
  z-index: -10;
}

.ar-button-container {
  margin: 0 auto 3rem auto;
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
  width: fit-content;
}

.button {
  width: 90vw;
  height: 2.625rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  background-color: var(--button);
  border: 3px solid rgba(255, 255, 255, 0.40);
  border-radius: 50px;
  font-family: "Open Sans";
  font-weight: 600;
  font-size: 1rem;
}

.button:hover {
  background-color: var(--button-hover);
}

.sketchfab-embed-wrapper {
  height: 100%;
  margin: 0;
}

.sketchfab-embed-wrapper iframe {
  height: 100%;
}

.fallback-texts {
  margin: 0 1.875rem;
}

p {
  font-family: "DINPro";
  color: #000;
  font-size: 1rem;
  font-weight: 400;
}

