:root {
  --ar-icon-size: 1.75rem;
  --wonder-blue: #0094aa;
  --ar-button-color: #0094aa;
  --ar-button-hover: #007283;
  --bgImgUrl: url("https://firebasestorage.googleapis.com/v0/b/augmented-shop-44400.appspot.com/o/images%2Fbg-desktop.svg?alt=media");
}

@font-face {
  font-family: "Poppins";
  src: url(/fonts/poppins/Poppins-SemiBold.ttf);
  font-weight: 600;
}

@font-face {
  font-family: "Trueno";
  src: url(/fonts/trueno/TruenoLt.ttf) format("truetype");
  font-weight: 400;
}

@font-face {
  font-family: "Trueno";
  src: url(/fonts/trueno/TruenoSBd.ttf) format("truetype");
  font-weight: 600;
}

body {
  font-size: 0.9rem;
  height: max-content;
}

.content-mobile {
  height: 100vh;
  height: 100dvh;
  background-color: var(--wonder-blue);
}

.product-name {
  font-family: "Poppins";
  font-weight: 600;
}

.name-container {
  background-color: rgba(255, 255, 255, 0.2);
  color: #fff;
  width: 100vw;
  font-family: "Poppins";
  padding: 0.9rem 0;
  margin-bottom: 1rem;
}

.headline {
  width: 80vw;
  font-family: "Trueno";
  font-weight: 600;
  color: #fff;
}

.instruction-container {
  align-items: center;
  font-family: "Trueno";
  font-size: 14px;
}

.main-container {
  gap: 4.375rem;
  padding-top: 3rem;
  padding-bottom: 1rem;
  position: relative;
  color: #fff;
}

.ar-button-container {
  padding-bottom: 1rem;
  margin-bottom: 2rem;
  width: fit-content;
}

.main-container:before {
  content: " ";
  display: block;
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  opacity: 0.2;
  background-image: url("https://firebasestorage.googleapis.com/v0/b/augmented-shop-44400.appspot.com/o/images%2Fwonder-pattern.svg?alt=media");
  background-repeat: no-repeat;
  background-position: 50% 0;
  background-size: cover;
}

ar-button {
  width: calc(100vw - (100vw * 10) / 100);
  height: 2.5rem;
  color: var(--wonder-blue);
  background-color: white;
  border-radius: 32px;
  font-family: "Trueno";
}

.logo {
  width: 4.375rem;
  height: 4.375rem;
  margin: 2.5rem 0 3.45rem 1.875rem;
}

ul {
  width: 80vw;
}
