/**
 * The following functions makes working with query string parameters easier and cleaner.
 * This is based on what the NocoDB REST API expects when it comes to query string parameters.
 * See https://docs.nocodb.com/developer-resources/rest-apis/#comparison-operators for more information.
 */

/**
 * Joins all the values in the given array, separated by commas.
 * @param { Array.<string> } array
 * @returns { string }
 */
export function fields(array) {
  return array.join(',');
}

/**
 * Formats the input parameters to match this pattern: '(field,operator,value)'
 * @param { string } field
 * @param { string } operator
 * @param { string } value
 * @returns { string }
 */
export function where(field, operator, value) {
  return `(${field},${operator},${value})`;
}
