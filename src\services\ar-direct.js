// @ts-check
import <PERSON><PERSON> from "../../vendors/Umami.js";
import IntlProvider from "./IntlProvider.js";
import { getProduct } from "../database.js";
import { resolveHostname } from "../host.js";
import ProductLocale from "../models/ProductLocale.js";
import { base64URLEncode } from "../utils/base64URL.js";
import routes from "../routes.js";
import { isMobileOrTablet } from "../utils/user-agent.js";

const html = String.raw;

export default async function arDirect(req, res) {
  if (!isMobileOrTablet(req.headers['user-agent'])) {
    const query = new URLSearchParams(req.query).toString();
    const url = `/${routes.landing}/${req.uid}${query ? `?${query}` : ""}`;
    return res.redirect(url);
  }

  const originUrl = new URL(req.url, `${req.protocol}://${req.hostname}`);

  /** @type {import("../models/Product.js").default} */
  const product = await getProduct(req.uid);

  if (
    !product ||
    !product.organization ||
    product.organization.status === "inactive"
  ) {
    return res.callNotFound();
  }

  const url = new URL(req.url, `${req.protocol}://${req.hostname}`);
  const { hostname, pathname } = url;
  const { userAgent } = req;
  const language = req.headers["accept-language"];
  const { referrer } = req.headers;

  // Applications on Railway.app run behind a proxy, the client’s direct IP is
  // not available through standard request attributes.
  // Railway.app uses the X-Envoy-External-Address header to forward the original client IP.
  const userIp = req.headers["X-Envoy-External-Address"] ||
    req.raw.ip ||
    req.ip;

  const intl = new IntlProvider({
    browserAcceptLanguage: language,
    defaultLanguage: product.defaultLang,
  });

  /** @type {ProductLocale} */
  const productLocale = intl.findBestOr(
    product.locales,
    ProductLocale.default(),
  );

  const host = resolveHostname();
  let arButtonLink = "";

  if (req.query.link || productLocale.link) {
    const base64Data = base64URLEncode(JSON.stringify({
      uid: product.uid,
      link: req.query.link || productLocale.link,
    }));

    arButtonLink = `${host}/${routes.arRedirect}/${base64Data}`;
  }

  track({ product, userIp, userAgent, hostname, language, pathname, referrer });

  if (req.userAgent.isAndroid) {
    return arDirectAndroid(res, product, productLocale, arButtonLink);
  }

  const isSafari = req.userAgent.family.includes("Safari");

  return arDirectIOS(
    res,
    product,
    productLocale,
    arButtonLink,
    originUrl.href,
    isSafari,
  );
}

/**
 * Tracks analytics data for AR product views using Umami
 * @param {Object} params - The tracking parameters
 * @param {import("../models/Product.js").default} params.product - The product being viewed
 * @param {string} params.userIp - The IP address of the user
 * @param {object} params.userAgent - The user agent information
 * @param {string} params.hostname - The hostname of the request
 * @param {string} params.language - The user's language preference
 * @param {string} params.pathname - The path of the current URL
 * @param {string} params.referrer - The referrer URL
 * @returns {void}
 */
function track({
  product,
  userIp,
  userAgent,
  hostname,
  language,
  pathname,
  referrer,
}) {
  const umami = new Umami({
    hostUrl: process.env.UMAMI_HOST,
    websiteId: product.organization.analyticsId,
    userAgent,
    userIp,
  });

  umami.trackView({
    hostname,
    language,
    title: `Wonder Shop - ${product.name}`,
    url: pathname,
    referrer,
  });

  umami.trackEvent("ar-view", {
    name: product.name,
    code: product.code,
  });
}

/**
 * @param {import("fastify").FastifyReply} res
 * @param {import("../models/Product.js").default} product
 * @param {ProductLocale} locale
 * @param {string} link
 * @param {string} originUrl
 * @param {boolean} isSafari
 */
function arDirectIOS(res, product, locale, link, originUrl, isSafari) {
  let href = `${product.usdzUrl}#`;
  if (locale.checkoutTitle) {
    href += `&checkoutTitle=${encodeURIComponent(locale.checkoutTitle)}`;
  }
  if (locale.checkoutSubtitle) {
    href += `&checkoutSubtitle=${encodeURIComponent(locale.checkoutSubtitle)}`;
  }
  if (locale.price) {
    href += `&price=${encodeURIComponent(locale.price)}`;
  }
  if (locale.callToAction) {
    href += `&callToAction=${encodeURIComponent(locale.callToAction)}`;
  }

  // Sharing functionality is broken on iOS 16+, so we need to provide the origin URL manually.
  // See https://bugs.webkit.org/show_bug.cgi?id=263878 for the official bug report.
  href += `&canonicalWebPageURL=${locale.canonicalWebPageUrl || originUrl}`;

  if (product.allowsContentScaling === true) href += "&allowsContentScaling=1";
  else href += "&allowsContentScaling=0";

  const htmlTemplate = html`
    <!DOCTYPE html>
    <html lang="en">
      <head>
        <meta charset="UTF-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>Wonder Shop - AR Direct</title>
      </head>
      <body onload="launchARQuickLook()">
        <script type="text/javascript">
          function launchARQuickLook() {
            const anchor = document.createElement("a");
            anchor.setAttribute("rel", "ar");

            ${browserAwareContent(isSafari, link)};

            anchor.setAttribute("href", "${href}");
            document.body.appendChild(anchor);
            anchor.appendChild(document.createElement("img"));
            anchor.click();
            console.log("Launching AR...");
          }
        </script>
      </body>
    </html>
  `;

  res.header("Content-Type", "text/html");
  res.code(200).send(htmlTemplate);
}

/**
 * This function is a hack around a false negative returned by the a.relList.supports('ar')
 * function when running on chrome iOS. We simply bypass the check in that case.
 * See https://bugs.webkit.org/show_bug.cgi?id=239135 for more details.
 * @param {boolean} isSafari
 * @returns {string}
 * @param {string} link
 */
function browserAwareContent(isSafari, link) {
  // TODO: instead of just logging a message when the iOS or macOS (aka iPad Pro)
  // device is not compatible, we should probably redirect to a fallback page,
  // similar to the android behavior.
  if (isSafari) {
    return `
      if (anchor.relList.supports('ar')) {
        anchor.appendChild(document.createElement('img'));
        anchor.setAttribute('rel', 'ar');
        ${link ? arButtonMarkup(link) : ""}
      } else {
        console.error('AR is not available.');
        return;
      }
    `.trim();
  }

  return `
    anchor.appendChild(document.createElement('img'));
    anchor.setAttribute('rel', 'ar');
    ${link ? arButtonMarkup(link) : ""}
  `.trim();
}

/**
 * @param {string} link
 * @returns {string}
 */
function arButtonMarkup(link) {
  return `
    anchor.addEventListener('message', (event) => {
      if (event.data === '_apple_ar_quicklook_button_tapped') {
        window.location.href = '${link}';
      }
    }, false);
  `;
}

/**
 * @param {import("fastify").FastifyReply} res
 * @param {import("../models/Product.js").default} product
 * @param {ProductLocale} locale
 * @param {string} link
 */
function arDirectAndroid(res, product, locale, link) {
  const host = resolveHostname();
  const fallbackURL = encodeURIComponent(
    `${host}/${routes.fallback}/${product.uid}`,
  );

  let href =
    `intent://arvr.google.com/scene-viewer/1.0?file=${product.glbUrl}&mode=ar_only`;

  if (locale.title) href += `&title=${locale.title}`;
  if (link) href += `&link=${link}`;
  if (!product.occlusion) href += "&disable_occlusion=true";

  href +=
    "#Intent;scheme=https;package=com.google.ar.core;action=android.intent.action.VIEW;";
  href += `S.browser_fallback_url=${fallbackURL};`;
  href += "end;";

  res.redirect(href);
}
