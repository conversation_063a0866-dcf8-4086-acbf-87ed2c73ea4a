:root {
  --ar-icon-size: 1.75rem;
  --font-color: #fff;
}

@font-face {
  font-family: "Overpass";
  src: url(/fonts/overpass/Overpass-SemiBold.ttf) format("truetype");
  font-weight: 600;
}

@font-face {
  font-family: "Overpass";
  src: url(/fonts/overpass/Overpass-Bold.ttf) format("truetype");
  font-weight: 700;
}

body {
  font-family: "Overpass";
  font-weight: 700;
}

.logo-container {
  margin-top: 5vh;
  justify-content: center;
}

.headline {
  font-family: "Overpass";
  width: 100%;
  font-weight: 700;
  color: #000;
  font-size: 1.625rem;
}

.instruction-container {
  padding-left: 1.875rem;
  padding-right: 1.875rem;
}

.instruction-title {
  font-family: "Overpass";
  font-weight: 700;
  font-size: 1.17em;
}

.main-container {
  gap: 4.375rem;
  padding-top: 3rem;
  padding-bottom: 1rem;
}

.main-container h2 {
  padding-left: 1.875rem;
  padding-right: 1.875rem;
}

.main-container:before {
  content: " ";
  display: block;
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  height: inherit;
  background-image: url("https://firebasestorage.googleapis.com/v0/b/bio-habitat-ws/o/images%2Fbg-bio-habitat.webp?alt=media");
  background-repeat: no-repeat;
  background-position: bottom;
  background-size: 120%;
  z-index: -10;
}

.ar-button-container {
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
}

.ar-button-container {
  padding: 0 1.875rem;
  width: 100%;
  margin-bottom: 2rem;
}

ar-button {
  height: 2.375rem;
  color: #1C1C1C;
  background-color: #E1BC7F;
  border-radius: 50px;
  font-family: "Overpass";
}

