:root {
  --ar-icon-size: 1.56rem;
  --ar-button-hover: #000;
}

@font-face {
  font-family: "Gotham";
  src: url(/fonts/gotham/Gotham-Regular.ttf) format("truetype");
  font-weight: 400;
}

@font-face {
  font-family: "Gotham";
  src: url(/fonts/gotham/Gotham-Light.otf) format("truetype");
  font-weight: 300;
}

.content-mobile {
  min-height: 90vh;
  flex: 1 0 auto;
}

.logo-container {
  align-items: center;
}

.logo {
  width: 5rem;
  margin: 2rem 0 2rem 1rem;
}

.button {
  width: 80%;
  height: 2.75rem;
  background-color: #000;
  font-family: "Gotham";
  font-weight: 350;
  font-size: 0.75rem;
  text-transform: uppercase;
}

p {
  font-family: "Gotham";
  color: #000;
  font-size: 0.8rem;
  font-weight: 400;
  letter-spacing: 0.32px;
  line-height: 1.125rem;
  text-transform: none;
  margin: 0 1.25rem;
}
