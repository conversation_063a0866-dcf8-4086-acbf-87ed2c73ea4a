:root {
  --ar-icon-size: 1.56rem;
  --button: #0053A0;
  --button-hover: #05417a;
}

@font-face {
  font-family: "Oxygen";
  src: url(/fonts/oxygen/Oxygen-Regular.ttf) format("truetype");
  font-weight: 400;
}

@font-face {
  font-family: "Open Sans";
  src: url(/fonts/opensans/OpenSans-SemiBold.ttf) format("truetype");
  font-weight: 600;
}

* {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  color: white;
}

html {
  height: 100vh;
  height: 100dvh;
  width: 100%;
  background-color: #FFF;
}

body {
  margin: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  min-height:100%;
}

.content-mobile {
  min-height: 90vh;
  flex: 1 0 auto;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.logo-container {
  width: 100vw;
  display: flex;
  text-align: center;
  margin: 2rem 0;
}

.logo {
  width: 7rem;
  margin: auto;
}

a {
  text-decoration: none;
}

.viewer-container {
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
}

.button {
  width: 90%;
  height: 3.125rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #FFF;
  border-radius: 5px;
  background-color: var(--button);
  font-family: "Open Sans";
  font-weight: 600;
  font-size: 1rem;
}

.button:hover {
  background-color: var(--button-hover);
}

.sketchfab-embed-wrapper {
  width: 100%;
  background-color: #fff;
  margin-bottom: 2rem;
}

.sketchfab-embed-wrapper iframe {
  width: 100%;
  min-height: 300px;
  height:100%;
}

p {
  font-family: "Oxygen";
  color: #000;
  font-size: 0.8rem;
  font-weight: 400;
  letter-spacing: 0.32px;
  line-height: 1.125rem;
  text-transform: none;
  margin: 0 1.75rem;
}
