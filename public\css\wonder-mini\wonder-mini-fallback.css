:root {
  --ar-icon-size: 1.56rem;
  --wonder-blue: #0094aa;
  --ar-button-hover: #42625f;
}

@font-face {
  font-family: "Poppins";
  src: url(/fonts/poppins/Poppins-SemiBold.ttf);
  font-weight: 600;
}

@font-face {
  font-family: "Trueno";
  src: url(/fonts/trueno/TruenoLt.ttf) format("truetype");
  font-weight: 400;
}

@font-face {
  font-family: "Trueno";
  src: url(/fonts/trueno/TruenoSBd.ttf) format("truetype");
  font-weight: 600;
}

body {
  min-height: 100%;
}

.content-mobile {
  min-height: 90vh;
  flex: 1 0 auto;
}

.logo-container {
  width: 100vw;
}

.logo {
  width: 4.375rem;
  height: 4.375rem;
  margin: 2rem 0 5vh 2rem;
  margin: 2rem 0 5dvh 2rem;
}

.viewer-container {
  margin-bottom: 10vh;
  margin-bottom: 10dvh;
}

.button {
  position: absolute;
  height: 3.125rem;
  color: var(--wonder-blue);
  border-radius: 50px;
  border: solid 3px var(--wonder-blue);
  background-color: white;
  bottom: -1.563rem;
}

.sketchfab-embed-wrapper {
  padding: 1vh 0;
  margin-top: 5vh;
  border-top: solid 1px #626f65;
  border-bottom: solid 1px #626f65;
}

h1 {
  font-family: "Trueno";
  color: var(--wonder-blue);
  font-size: 1.625rem;
  font-weight: 400;
  line-height: 1.75rem;
  margin-bottom: 1rem;
}

p {
  font-family: "Trueno";
  color: #000;
  font-size: 0.875rem;
  font-weight: 400;
  text-align: center;
  margin: 0 2rem;
}
