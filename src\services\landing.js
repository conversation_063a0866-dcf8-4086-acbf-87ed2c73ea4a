/* eslint-disable dot-notation */
// @ts-check

import IntlProvider from "./IntlProvider.js";
import { getProduct, getTemplateOrDefault } from "../database.js";
import ProductLocale from "../models/ProductLocale.js";
import TemplateLocale from "../models/TemplateLocale.js";
import routes from "../routes.js";
import getViewOrDefault from "../utils/views.js";
import { base64URLEncode } from "../utils/base64URL.js";
import fbTranslations from "../i18n/fallback-desktop.js";
import getUserLanguage from "../utils/user-language.js";

/**
 * @param { import('fastify').FastifyRequest } req
 * @param { import('fastify').FastifyReply } res
 */
export default async function landing(req, res) {
  const originUrl = new URL(req.url, `${req.protocol}://${req.hostname}`);
  // @ts-ignore This is injected by the uid-validation middleware
  const product = await getProduct(req.uid);

  if (
    !product ||
    !product.organization ||
    product.organization.status === "inactive"
  ) {
    return res.callNotFound();
  }

  const template = await getTemplateOrDefault(product.organization.templateId);

  if (!template) {
    return res.callNotFound();
  }

  const fallbackTranslations = getUserLanguage(req, fbTranslations);

  /** @type	{string} */
  const acceptLang = req.query["lang"] || req.headers["accept-language"];

  const intl = new IntlProvider({
    browserAcceptLanguage: acceptLang,
    defaultLanguage: product.defaultLang,
  });

  /** @type {ProductLocale} */
  const productLocale = intl.findBestOr(
    product.locales,
    ProductLocale.default(),
  );

  /** @type {TemplateLocale} */
  const templateLocale = intl.findBestOr(
    template.locales,
    TemplateLocale.default(),
  );

  const host = `${req.protocol}://${req.hostname}`;
  let arButtonLink = "";

  if (req.query["link"] || productLocale.link) {
    const base64Data = base64URLEncode(JSON.stringify({
      uid: product.uid,
      link: req.query["link"] || productLocale.link,
    }));

    arButtonLink = `${host}/${routes.arRedirect}/${base64Data}`;
  }

  const viewProps = {
    productCode: product.code,
    name: product.name,
    orgName: product.organization.name,
    analyticsId: product.organization.analyticsId,
    logo: product.organization.logoUrl,
    favicon: product.organization.faviconUrl,
    webclip: product.organization.webclipUrl,
    bucket: product.organization.bucket,
    modelName: product.modelName,
    src: product.glbUrl,
    title: productLocale.title,
    occlusion: product.occlusion,
    fallbackUrl: `${host}/${routes.fallback}/${product.uid}`,
    link: arButtonLink,
    iosSrc: product.usdzUrl,
    checkoutTitle: productLocale.checkoutTitle,
    checkoutSubtitle: productLocale.checkoutSubtitle,
    callToAction: productLocale.callToAction,
    iosLink: arButtonLink,
    price: productLocale.price,
    allowsContentScaling: product.allowsContentScaling,
    canonicalWebPageUrl: productLocale.canonicalWebPageUrl || originUrl.href,
    productButtonName: productLocale.buttonName,
    sketchfabModelId: product.sketchfabModelId,
    fallbackTranslations,
    ...templateLocale,
  };

  return res.view(await getViewOrDefault("landings", template.name), viewProps);
}
