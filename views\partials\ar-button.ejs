<ar-button 
  <% if (locals.name) { %>
  data-product-name="<%= name %>"
  <% } %>

  <% if (locals.productCode) { %>
  data-product-code="<%= productCode %>"
  <% } %>

  src="<%= src %>"
  ios-src="<%= iosSrc %>"
  class="icon-ar"

  <%# Android attributes %>
  
  <% if (locals.title) { %>
  title="<%= title %>"
  <% } %>

  <% if (locals.occlusion && occlusion == true) { %>
  occlusion
  <% } %>

  <% if (locals.fallbackUrl) { %>
  fallback-url="<%= fallbackUrl %>"
  <% } %>

  <% if (locals.link) { %>
  link="<%= link %>"
  <% } %>

  <%# iOS attributes %>

  <% if (locals.checkoutTitle) { %>
  checkout-title="<%= checkoutTitle %>"
  <% } %>
    
  <% if (locals.checkoutSubtitle) { %>
  checkout-subtitle="<%= checkoutSubtitle %>"
  <% } %>

  <% if (locals.callToAction) { %>
  call-to-action="<%= callToAction %>"
  <% } %>

  <% if (locals.iosLink) { %>
  ios-link="<%= iosLink %>"
  <% } %>

  <% if (locals.price) { %>
  price="<%= price %>"
  <% } %>

  <% if (locals.canonicalWebPageUrl) { %>
  canonical-web-page-url="<%= canonicalWebPageUrl %>"
  <% } %>

  <% if (locals.allowsContentScaling && allowsContentScaling === true) { %>
  allows-content-scaling="1"
  <% } else { %>
  allows-content-scaling="0"
  <% } %>
><span><%= buttonName %></span></ar-button>
