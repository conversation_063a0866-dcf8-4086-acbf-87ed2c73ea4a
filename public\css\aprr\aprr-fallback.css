:root {
    --ar-icon-size: 1.75rem;
    --red : #E32218;
    --ar-button-hover : #b4160e;
  }
  
  @font-face {
    font-family: "Poppins";
    src: url(/fonts/poppins/Poppins-SemiBold.ttf);
    font-weight: 600;
  }
  @font-face {
    font-family: "SourceSans3";
    src: url(/fonts/sourcesans/SourceSans3-Bold.ttf) format("truetype");
    font-weight: 700;
    font-style: normal;
    font-display: swap;
  }
  
  * {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    color: black;
  }
  html {
    height: 100vh;
    height: 100dvh;
    width: 100%;
    background-color: #fff;
  }
  body {
    margin: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    justify-content: space-evenly;
    padding-top: 2vh;
    padding-bottom: 2vh;
    min-height:100%;
  }
  a {
    text-decoration: none;
  }
  .button {
    width: 16rem;
    height: 3.2rem;
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 32px;
    border: solid 3px #ffffffb6;
    background-color: var(--red);
    font-family: "Poppins";
    font-weight: 600;
    font-size: 1rem;
    margin: 1.2rem 0;
  }
  .button:hover {
    background-color: var(--ar-button-hover);
  }
  .sketchfab-embed-wrapper {
    width: 100%;
    padding: 1vh 0;
    background-color: #fff;
    margin-bottom: 2vh;
  }
  .sketchfab-embed-wrapper iframe {
    width: 100%;
    min-height: 300px;
    height:45vh;
  }
  h1 {
    font-family: SourceSans3, sans-serif;
    color: var(--red);
    font-size: 1.25rem;
    line-height: 1.75rem;
  }
  p {
    font-family: SourceSans3, sans-serif;
    font-size: 0.9rem;
    line-height: 1.3rem;
    text-align: center;
    text-transform: none;
    margin: 0 15%;
    padding-bottom: 1vh;
  }
  