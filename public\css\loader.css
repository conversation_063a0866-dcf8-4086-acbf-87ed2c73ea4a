:root {
  --color-main: #B0B0B0;    /* gris principal */
  --color-right: #8F8F8F;   /* légèrement plus foncé */
  --color-left: #A5A5A5;    /* légèrement plus clair */
  --color-shadow: #E0E0E0;  /* ombre / fond inférieur */
  --size: 32px;
  --duration: 800ms;
}

model-viewer [slot="progress-bar"] {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  pointer-events: none;
  z-index: 10;
  transition: opacity 0.25s ease, visibility 0.25s;
}

.boxes {
  height: calc(var(--size) * 2);
  width: calc(var(--size) * 3);
  position: relative;
  transform-style: preserve-3d;
  transform: rotateX(60deg) rotateZ(45deg) translateZ(0px);
}

.boxes .box {
  width: var(--size);
  height: var(--size);
  top: 0;
  left: 0;
  position: absolute;
  transform-style: preserve-3d;
}

.boxes .box:nth-child(1) {
  transform: translate(100%, 0);
  animation: box1 var(--duration) linear infinite;
}

.boxes .box:nth-child(2) {
  transform: translate(0, 100%);
  animation: box2 var(--duration) linear infinite;
}

.boxes .box:nth-child(3) {
  transform: translate(100%, 100%);
  animation: box3 var(--duration) linear infinite;
}

.boxes .box:nth-child(4) {
  transform: translate(200%, 0);
  animation: box4 var(--duration) linear infinite;
}

.boxes .box>div {
  --background: var(--color-main);
  --top: auto;
  --right: auto;
  --bottom: auto;
  --left: auto;
  --translateZ: calc(var(--size) / 2);
  --rotateY: 0deg;
  --rotateX: 0deg;
  position: absolute;
  width: 100%;
  height: 100%;
  background: var(--background);
  top: var(--top);
  right: var(--right);
  bottom: var(--bottom);
  left: var(--left);
  transform: rotateY(var(--rotateY)) rotateX(var(--rotateX)) translateZ(var(--translateZ));
}

.boxes .box>div:nth-child(1) {
  --top: 0;
  --left: 0;
}

.boxes .box>div:nth-child(2) {
  --background: var(--color-right);
  --right: 0;
  --rotateY: 90deg;
}

.boxes .box>div:nth-child(3) {
  --background: var(--color-left);
  --rotateX: -90deg;
}

.boxes .box>div:nth-child(4) {
  --background: var(--color-shadow);
  --top: 0;
  --left: 0;
  --translateZ: calc(var(--size) * 3 * -1);
}

@keyframes box1 {

  0%,
  50% {
    transform: translate(100%, 0);
  }

  100% {
    transform: translate(200%, 0);
  }
}

@keyframes box2 {
  0% {
    transform: translate(0, 100%);
  }

  50% {
    transform: translate(0, 0);
  }

  100% {
    transform: translate(100%, 0);
  }
}

@keyframes box3 {

  0%,
  50% {
    transform: translate(100%, 100%);
  }

  100% {
    transform: translate(0, 100%);
  }
}

@keyframes box4 {
  0% {
    transform: translate(200%, 0);
  }

  50% {
    transform: translate(200%, 100%);
  }

  100% {
    transform: translate(100%, 100%);
  }
}

.boxes.hidden {
  opacity: 0;
  pointer-events: none;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2rem;
}

.loading-percentage {
  font-family: "PulpDisplay", sans-serif;
  font-size: 1rem;
  font-weight: 500;
  color: #666;
  text-align: center;
  opacity: 1;
  transition: opacity 0.25s ease;
}

.loading-container.hidden {
  opacity: 0;
  pointer-events: none;
}

.loading-container.hidden .loading-percentage {
  opacity: 0;
}