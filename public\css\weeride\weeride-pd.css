:root {
  --ar-button-color: #5cb287;
  --ar-button-hover: #5d8873;
  --font-header: "GolosText";
  --font-body: "GolosText";
}

@font-face {
  font-family: "GolosText";
  src: url(/fonts/GolosText/GolosText-VariableFont_wght.ttf) format("truetype");
  font-weight: 400;
  font-style: normal;
}

.logo {
  width: 100%;
}

.logo-container {
  max-width: 50%;
}

.logo-desktop {
  width: 100% !important;
  max-width: 350px !important;
  max-height: 150px !important;
  height: auto !important;
  margin: 3rem 12vw !important;
  padding: 0% !important;
}

.logo-container-desktop {
  width: 100%;
  max-height: 300px;
  background: white !important;
}

.name-container-desktop {
  background-color: transparent !important;
  font-family: var(--font-header) !important;
}

.headline-desktop {
  font-family: var(--font-body), sans-serif !important;
}
