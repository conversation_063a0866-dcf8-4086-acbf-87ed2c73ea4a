# Wonder Shop

Augmented Shop API is a Node.js backend to serve AR content through direct AR or with a template landing page.

## Prerequisites

You need to be familiar with [AR Quick Look](https://developer.apple.com/documentation/arkit/previewing_a_model_with_ar_quick_look) (iOS) and [Scene Viewer](https://developers.google.com/ar/develop/scene-viewer) (Android) to fully understand what this API do. You can also take a look at <PERSON>'s own [ar-button](https://github.com/Wonder-Partners/ar-button) package to get a grasp on how AR works on the web.

### Check your active node version

This project is built with node v18.14.0 LTS. Download and manage your node versions with [Volta](https://volta.sh/).

Run `node -v` to check what version of node is installed on your system.

The project's node version is pinned in the package.json file:

```json
"volta": {
  "node": "18.14.0"
}
```

### Fill the environment variables

You can get them in the NocoDB's dashboard.

```text
DATABASE_URL=<api_key>
PORT=3000
```

### Linting and styling extensions

This project use [`eslint`](https://marketplace.visualstudio.com/items?itemName=dbaeumer.vscode-eslint) both as a linter and style enforcer. The configuration extends the [`airbnb-base`](https://github.com/airbnb/javascript) config.

## Quick start

### Install dependencies

```bash
npm install
```

### Running it locally

```bash
npm run dev:staging
```

Go to [`http://localhost:3000/health`](http://localhost:3000/health) to verify that the server is running correctly. You should see a 'OK' message.

> Tip: the dev server is "hot reloading" when you make changes to the application.

## Creating new template

```bash
npm run create
```

## Hosting and deployment

TODO

### Specifying the node version

Railway uses Nixpacks under the hood. The default node version is `16`, but this api is built with node `18`. To tell Railway to use node version `18`, add an environment variable on the service:

```text
NIXPACKS_NODE_VERSION=18
```

For more information, check out [the official Nixpacks documentation](https://nixpacks.com/docs/providers/node) and [the Railway variables documentation](https://docs.railway.app/develop/variables).

## Tech stack

This project is built with the following libraries:

- [**Fastify**](https://www.fastify.io/): web framework

## Documentation

You can find an exhaustive documentation [here](https://www.notion.so/wonderpartners/fa8756d231dd42ca9f482ce30cc5c8e2?v=8946c838d4614d2284e68d75ef280dfa).
