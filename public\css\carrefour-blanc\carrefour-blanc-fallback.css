:root {
  --ar-icon-size: 1.56rem;
  --button: #F21C72;
  --button-hover: #ce1660;
}

@font-face {
  font-family: "Ubuntu";
  src: url(/fonts/ubuntu/Ubuntu.ttf) format("truetype");
  font-weight: 400;
}

@font-face {
  font-family: "Open Sans";
  src: url(/fonts/opensans/OpenSans-SemiBold.ttf) format("truetype");
  font-weight: 600;
}

body {
  margin: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  min-height:100%;
}

.content-mobile {
  min-height: 90vh;
  width: 100%;
  flex: 1 0 auto;
  gap: 1rem;
}

.logo-container {
  display: flex;
  text-align: center;
  width: fit-content;
  margin: 2rem 0;
}

.logo {
  width: 4rem;
  margin: auto;
}

.button {
  width: 90%;
  height: 3.125rem;
  border-radius: 50px;
  background-color: var(--button);
  font-family: "Open Sans";
}

.sketchfab-embed-wrapper {
  margin-bottom: 2rem;
}

.sketchfab-embed-wrapper iframe {
  height:100%;
}

.instruction-container{
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  gap: 1.5rem;
  padding: 2rem 2rem 0 2rem;
}

p {
  font-family: "Ubuntu";
  color: #000;
  font-weight: 400;
}
