@font-face {
  font-family: "Nunito";
  src: url(/fonts/Nunito/Nunito-VariableFont_wght.ttf) format("truetype");
  font-weight: 400;
}

@font-face {
  font-family: "FunnelDisplay";
  src: url(/fonts/FunnelDisplay/FunnelDisplay-VariableFont_wght.ttf)
    format("truetype");
  font-weight: 700;
}

* {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}

body {
  background-color: #dfdece;
}

.logo-container {
  margin-top: 5vh;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.logo {
  width: 201px;
}

.background-header-image {
  position: absolute;
  top: -3vh;
  left: -26%;
  max-width: 272px;
  max-height: 206px;
  opacity: 0.3;
}

.background-footer-image {
  position: absolute;
  bottom: -5.21vh;
  right: 0px;
  max-width: 190px;
  max-height: 268px;
  opacity: 0.2;
}
.text-container {
  text-align: center;
  padding: 25px;
}

.title {
  font-family: "FunelDisplay", sans-serif;
  color: #112c22;
}

.subtitle {
  font-family: "FunelDisplay", sans-serif;
  font-weight: 700;
  min-width: 16.25rem;
  font-size: 1.375rem;
  margin-bottom: 1.875rem;
}

.viewer-text {
  font-family: "Nunito", sans-serif;
  font-weight: 400;
  font-size: 0.938rem;
  letter-spacing: 0.02rem;
}

.sketchfab-embed-wrapper {
  background: none;
}
