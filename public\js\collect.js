// @ts-nocheck
/* eslint-disable */

const { umami, productData } = window;
const productName = productData?.name;
const arButtons = document.querySelectorAll("ar-button");

arButtons.forEach((arButton) => {
  const productName = arButton.getAttribute("data-product-name");
  const productCode = arButton.getAttribute("data-product-code");

  if (umami && arButton && productName) {
    arButton.addEventListener("click", () => {
      umami.track("ar-view", {
        name: productName,
        code: productCode,
      });
    });
  }
});
