:root {
  --ar-icon-size: 1.75rem;
  --ar-button-color: #0053A0;
}

@font-face {
  font-family: "Oxygen";
  src: url(/fonts/oxygen/Oxygen-Regular.ttf) format("truetype");
  font-weight: 400;
}

@font-face {
  font-family: "Oxygen";
  src: url(/fonts/oxygen/Oxygen-Bold.ttf) format("truetype");
  font-weight: 700;
}

@font-face {
  font-family: "Open Sans";
  src: url(/fonts/opensans/OpenSans-Medium.ttf) format("truetype");
  font-weight: 600;
}

* {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  color: #000;
}

html {
  height: 100vh;
  height: 100dvh;
  width: 100%;
  background-color: #fff;
}

body {
  position: relative;
  margin: 0;
  font-size: 0.9rem;
  line-height: 1.25rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  min-height: 100vh;
  min-height: 100dvh;
  height: max-content;
  padding: 0;
}

.logo {
  width: 7rem;
  margin: 2.5rem 0 3.45rem 1.875rem;
}

.logo-container {
  text-align: left;
}

.content-mobile {
  flex: 0 0 auto;
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 90vh;
  height: 90dvh;
}

.headline {
  font-family: "Oxygen";
  width: 100%;
  font-weight: 700;
  color: #000;
  line-height: 120%;
  font-size: 1.625rem;
  margin-bottom: 1rem;
}

.instruction-container {
  display: flex;
  flex-direction: column;
  padding-left: 1.875rem;
  padding-right: 1.875rem;
}

.main-container {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4.1rem;
  padding-top: 3rem;
  padding-bottom: 1rem;
  position: relative;
}

.main-container h2 {
  padding-left: 1.875rem;
  padding-right: 1.875rem;
}

.ar-button-container {
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
}

.ar-button-container {
  margin: 0 auto;
  width: fit-content;
}

ar-button {
  width: calc(100vw - (100vw * 10) / 100);
  height: 3.125rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  background-color: var(--ar-button-color);
  border: solid 2px #fff;
  border-radius: 5px;
  font-family: "Open Sans";
  font-weight: 600;
  font-size: 1rem;
}

ul {
  width: 100%;
}

ul p {
  width: 100%;
  font-size: 0.875rem;
  line-height: 1.125rem;
  font-family: 'Oxygen';
  font-weight: 400;
  letter-spacing: 0.02rem;
  color: #000;
}
