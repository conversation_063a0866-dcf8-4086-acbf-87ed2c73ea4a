:root {
  --ar-icon-size: 1.75rem;
  --cadiou-green: #626f65;
  --ar-button-hover: #42625f;
}

@font-face {
  font-family: "Poppins";
  src: url(/fonts/poppins/Poppins-SemiBold.ttf);
  font-weight: 600;
}

@font-face {
  font-family: "SourceSans3";
  src: url(/fonts/sourcesans/SourceSans3-Bold.ttf) format("truetype");
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "SourceSans3";
  src: url(/fonts/sourcesans/SourceSans3-Medium.ttf) format("truetype");
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

body {
  font-size: 0.9rem;
}

.name-container {
  background-color: rgba(98, 111, 101, 0.2);
  padding: 0.9rem 0;
  margin-bottom: 5vh;
  margin-bottom: 5dvh;
  font-family: "Poppins";
}

.headline {
  width: 60%;
  font-weight: 400;
  font-size: 1.9rem;
  line-height: 2.3rem;
  color: var(--cadiou-green);
  font-family: "Poppins";
  margin-bottom: 1.375rem;
}

.logo-container {
  margin: 7vh auto 5vh;
  margin: 7dvh auto 5dvh;
  max-width: 100%;
  object-fit: contain;
  align-items: center;
  justify-content: center;
}

.logo {
  max-height: 3rem;
  max-width: 13.125rem;
}

.main-container {
  gap: 4.375rem;
  padding: 0 3.5rem;
  padding-bottom: 1rem;
  position: relative;
  color: #fff;
}

.ar-button-container {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

ar-button {
  width: calc(100vw - (100vw * 25) / 100);
  height: 2.5rem;
  border-radius: 4px;
  background-color: #fff;
  color: #000;
}

@media (hover: hover) {
  ar-button:hover {
    background-color: var(--ar-button-hover);
  }
}

ar-button:active {
  background-color: var(--ar-button-hover);
}

.instruction-container {
  background-color: var(--cadiou-green);
  color: #FFF;
  justify-content: center;
  align-items: center;
  max-width: calc(100vw - (100vw / 10));
  padding: 1.65rem 1.5rem;
  margin-bottom: 1.125rem;
}

.instruction-title {
  font-family: SourceSans3, sans-serif;
  font-size: 1.25rem;
  line-height: 1.5rem;
  font-weight: 700;
  text-align: left;
  margin-bottom: 0.75rem;
}

.separator {
  background-color: #fff;
  height: 1px;
  width: 75%;
  margin-bottom: 0.75rem;
}

ul {
  width: 100%;
}

ul p {
  margin-bottom: 0.5rem;
  font-size: 1rem;
  line-height: 1.6rem;
  font-weight: 500;
  text-transform: none;
}