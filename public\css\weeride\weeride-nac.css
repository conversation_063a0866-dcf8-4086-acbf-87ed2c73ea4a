:root {
  --ar-button-color: #2351f5;
  --ar-button-hover: #3e5cca;
  --background-color: #f8f8f8;
  --font-header: "Scandia";
  --font-body: "Scandia";
}

@font-face {
  font-family: "Scandia";
  src: url(/fonts/scandia/Scandia-Regular.ttf) format("truetype");
  font-weight: 400;
  font-style: normal;
}

html {
  background-color: var(--background-color);
}

.logo {
  width: 100%;
}

.logo-container {
  max-width: 50%;
}

.logo-desktop {
  width: 100% !important;
  max-width: 350px !important;
  max-height: 150px !important;
  height: auto !important;
  margin: 3rem 12vw !important;
  padding: 0% !important;
}

.logo-container-desktop {
  width: 100%;
  max-height: 300px;
  background: white !important;
}

.name-container-desktop {
  background-color: transparent !important;
  font-family: var(--font-header) !important;
}

.headline-desktop {
  font-family: var(--font-body), sans-serif !important;
}
