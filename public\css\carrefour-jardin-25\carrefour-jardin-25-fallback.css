@font-face {
  font-family: "ProximaNovaCond";
  src: url(/fonts/proxima-nova/ProximaNovaCondBold.ttf) format("truetype");
  font-weight: 700;
}

@font-face {
  font-family: "ProximaNova";
  src: url(/fonts/proxima-nova/ProximaNovaBold.ttf) format("truetype");
  font-weight: 700;
}

@font-face {
  font-family: "ProximaNova";
  src: url(/fonts/proxima-nova/ProximaNovaRegular.ttf) format("truetype");
  font-weight: 400;
}

.logo-container {
  margin-top: 5vh;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.background-header-image {
  position: absolute;
  top: -3vh;
  left: -26%;
  max-width: 272px;
  max-height: 206px;
  opacity: 0.3;
}

.background-footer-image {
  position: absolute;
  bottom: -5.21vh;
  right: 0px;
  max-width: 190px;
  max-height: 268px;
  opacity: 0.2;
}

.logo {
  width: 5rem;
}

.text-container {
  text-align: center;
}

.title {
  font-family: "ProximaNovaCond", sans-serif;
  color: #005aa7;
}

.subtitle {
  font-family: "ProximaNova", sans-serif;
  font-weight: 700;
  min-width: 16.25rem;
  font-size: 1.375rem;
  margin-bottom: 1.875rem;
}

.viewer-text {
  font-family: "ProximaNova", sans-serif;
  font-weight: 400;
  font-size: 0.938rem;
  letter-spacing: 0.02rem;
}

.sketchfab-embed-wrapper {
  background: none;
}
