:root {
  --ar-button-color: #e8bf8d;
  --ar-button-hover: #9d815f;
}

@font-face {
  font-family: "Open Sans";
  src: url(/fonts/opensans/OpenSans-SemiBold.ttf) format("truetype");
  font-weight: 600;
}

@font-face {
  font-family: "Open Sans";
  src: url(/fonts/opensans/OpenSans-Bold.ttf) format("truetype");
  font-weight: 700;
}

@font-face {
  font-family: "Montserrat";
  src: url(/fonts/montserrat/Montserrat-SemiBold.ttf) format("truetype");
  font-weight: 600;
}

@font-face {
  font-family: "Lato";
  src: url(/fonts/lato/Lato-Regular.ttf) format("truetype");
  font-weight: 400;
}

.content-mobile {
  justify-content: space-evenly;
}

.logo {
  margin: 5vh 0 5vh 1.875rem;
}

.name-container {
  background-color: #7e7e7e;
  color: #fff;
  box-shadow: 0px 4px 4px 0px rgba(51, 51, 51, 0.2);
  font-family: "Open Sans";
  padding: 1.65vh 1.875rem;
  text-align: center;
}

.main-container {
  padding: 3rem 7vw 6rem 7vw;
}

.headline {
  font-family: "Montserrat", sans-serif;
}

ul p {
  font-family: "Lato", sans-serif;
  font-weight: 600;
  letter-spacing: 0.02rem;
  line-height: normal;
}

.ar-button-container {
  width: 100%;
}

ar-button {
  height: 2.8rem;
  border-radius: 32px;
  font-family: "Open Sans";
  font-weight: 700;
  font-size: 1.063rem;
  color: #000;
}