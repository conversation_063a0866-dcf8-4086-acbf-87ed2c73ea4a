:root {
  --ar-icon-size: 1.56rem;
  --wonder-blue: #0094AA;
  --ar-button-hover: #42625F;
}

@font-face {
  font-family: "Poppins";
  src: url(/fonts/poppins/Poppins-SemiBold.ttf);
  font-weight: 600;
}

@font-face {
  font-family: "Trueno";
  src: url(/fonts/trueno/TruenoLt.ttf) format("truetype");
  font-weight: 400;
}

@font-face {
  font-family: "Trueno";
  src: url(/fonts/trueno/TruenoSBd.ttf) format("truetype");
  font-weight: 600;
}

.content-mobile {
  min-height: 90vh;
  flex: 1 0 auto;
}

.logo-container {
  width: 100vw;
  align-items: center;
}

.logo {
  width: 4.375rem;
  height: 4.375rem;
  margin: 2rem 0 2rem 1rem;
}

.button {
  position: absolute;
  height: 3.125rem;
  color: var(--wonder-blue);
  border-radius: 50px;
  border: solid 3px var(--wonder-blue);
  background-color: white;
  bottom: -1.563rem;
}

.sketchfab-embed-wrapper {
  padding: 1vh 0;
  margin-top: 1vh;
  border-top: solid 1px #626F65;
  border-bottom: solid 1px #626F65;
}

.sketchfab-embed-wrapper iframe {
  min-height: 400px;
  height:60vh;
}

p {
  font-family: "Trueno";
  color: #000;
  font-weight: 400;
  margin: 0 1.25rem;
}