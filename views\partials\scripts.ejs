
<% if (process.env.RAILWAY_ENVIRONMENT_NAME === 'production' || process.env.RAILWAY_ENVIRONMENT_NAME === 'staging') { %>
<script
  src="https://js-de.sentry-cdn.com/a637fe611f9b20b5de7f7a07c14660cf.min.js"
  crossorigin="anonymous"
></script>
  
<script>
  window.sentryOnLoad = function () {
    Sentry.init({
      dsn: "<%= process.env.SENTRY_DSN %>",
      environment: "<%= process.env.RAILWAY_ENVIRONMENT_NAME || 'development' %>",
    });
  };
</script>
<% } %>

<script type="module" src="/js/fallback-desktop.js"></script>

<script type="module" src="https://cdn.jsdelivr.net/npm/@wonder-partners/ar-button@1.0.3/main.min.js"></script>

<% if (locals.analyticsId) { %>
<script defer src="<%= process.env.UMAMI_HOST + '/script.js' %>" data-website-id="<%= analyticsId %>"></script>
<script defer src="/js/collect.js"></script>
<% } %>