:root {
  --ar-icon-size: 1.75rem;
  --quality-spa-dark-blue: #1979c2;
  --quality-spa-light-blue: #64abdd;
  --quality-spa-dark-grey: #4f4d4a;
  --ar-button-light-hover: #3a6e93;
  --ar-button-dark-hover: #0e436b;
}

@font-face {
  font-family: "Poppins";
  src: url(/fonts/poppins/Poppins-SemiBold.ttf);
  font-weight: 600;
}

@font-face {
  font-family: "SourceSans3";
  src: url(/fonts/sourcesans/SourceSans3-Bold.ttf) format("truetype");
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "SourceSans3";
  src: url(/fonts/sourcesans/SourceSans3-Medium.ttf) format("truetype");
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

body {
  font-size: 0.9rem;
  height: auto;
}

body:before {
  content: " ";
  display: block;
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  min-height: 100vh;
  height: 100%;
  opacity: 0.15;
  background-image: url("https://firebasestorage.googleapis.com/v0/b/quality-spa/o/images%2Fquality-spa-bg.webp?alt=media");
  background-repeat: no-repeat;
  background-position: 50% 0;
  background-size: cover;
  z-index: -10;
}

.content-mobile {
  gap: 3rem;
  height: 100%;
}

#viewer-section {
  position: absolute;
  top: 0;
  left: 0;
  flex: 0 0 auto;
  width: 100vw;
  height: 100vh;
  height: 100dvh;
  min-height: 100%;
  background-color: #fff;
  padding-top: 2rem;
  z-index: 20;
}

.head-section {
  display: flex;
  justify-content: baseline;
  align-items: center;
  margin-bottom: calc(1vh * 3);
  padding: 0 1.5rem;
}

#closeBtn {
  width: 1.5rem;
  height: 1.5rem;
}

.d-none {
  display: none;
}

.headline {
  font-family: "SourceSans3";
  font-weight: 400;
  font-size: 1.625rem;
  line-height: 1.8rem;
  color: var(--quality-spa-dark-grey);
  margin-bottom: calc(1vh * 3);
  padding: 0 1.5rem;
}

.viewer-headline {
  width: 100%;
  font-family: "SourceSans3";
  font-weight: 400;
  font-size: 1.625rem;
  line-height: 1.8rem;
  color: var(--quality-spa-dark-grey);
}

.logo {
  width: 4.375rem;
  height: 4.375rem;
  margin: 2.2rem 0 3.45rem 2rem;
}

.main-container, .modal-main-container {
  margin: 0 auto;
  gap: 2rem;
  width: auto;
}

.viewer-button {
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
  margin-bottom: 3rem;
}

ar-button,
button {
  width: calc(100vw - (100vw * 25) / 100);
  height: 3.125rem;
  border-radius: 32px;
  border: 3px solid rgba(255, 255, 255, 0.8);
  background-color: var(--quality-spa-light-blue);
  color: #fff;
}

button {
  font-family: "Poppins";
  font-weight: 600;
  font-size: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #1979c2;
}

ar-button:nth-child(even) {
  background-color: var(--quality-spa-dark-blue);
}

ar-button:hover {
  background-color: #3a6e93;
}

button:hover {
  background-color: #0e436b;
}

ar-button:hover:nth-child(even) {
  background-color: var(--ar-button-dark-hover);
}

.instruction-container {
  justify-content: center;
  max-width: calc(100vw - (100vw / 10));
  padding: 0 1.5rem;
  gap: 2rem;
}

.instruction-title {
  font-family: SourceSans3, sans-serif;
  text-align: left;
  margin-bottom: 0.9rem;
  color: var(--quality-spa-dark-grey);
}

#viewer-section .sketchfab-embed-wrapper {
  width: 100%;
  height: auto;
  padding: 1vh 0;
  margin-top: 1.8rem;
}

#viewer-section .sketchfab-embed-wrapper iframe {
  width: 100%;
  min-height: 300px;
  height: 50vh;
}

.modal-in {
  z-index: 0;
  transform: scale(1);
  animation: moveUp 0.5s cubic-bezier(0.165, 0.84, 0.44, 1) forwards;
}

.modal-out {
  animation: moveDown 0.5s cubic-bezier(0.165, 0.84, 0.44, 1) forwards;
}

@keyframes moveUp {
  0% {
    transform: translateY(100vh);
  }
  100% {
    transform: translateY(0);
  }
}

@keyframes moveDown {
  0% {
    transform: translateY(0px);
  }
  100% {
    transform: translateY(100vh);
  }
}