:root {
  --ar-icon-size: 1.56rem;
  --wonder-blue: #0094AA;
  --ar-button-hover: #42625F;
}

@font-face {
  font-family: "Poppins";
  src: url(/fonts/poppins/Poppins-SemiBold.ttf);
  font-weight: 600;
}

@font-face {
  font-family: "Open Sans";
  src: url(/fonts/opensans/OpenSans-Light.ttf) format("truetype");
  font-weight: 300;
}

@font-face {
  font-family: "Open Sans";
  src: url(/fonts/opensans/OpenSans-Regular.ttf) format("truetype");
  font-weight: 400;
}

.content-mobile {
  min-height: 90vh;
  flex: 1 0 auto;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.logo-container {
  align-items: center;
}

.logo {
  width: 5rem;
  margin: 2rem 0 2rem 1rem;
}

.button {
  width: 80%;
  height: 3.125rem;
  color: #FFF;
  border-radius: 50px;
  background-color: #002F6C;
  font-family: "Open Sans";
  font-weight: 300;
  font-size: 1rem;
}

.sketchfab-embed-wrapper {
  margin-bottom: 2rem;
}

.sketchfab-embed-wrapper iframe {
  min-height: 400px;
  height:100%;
}

p {
  font-family: "Open Sans";
  color: #000;
  margin: 0 1.25rem;
}
