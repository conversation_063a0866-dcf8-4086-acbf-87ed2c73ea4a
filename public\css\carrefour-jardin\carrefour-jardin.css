:root {
  --ar-icon-size: 1.75rem;
  --ariel-color: #000;
  --font-color: #fff;
}

@font-face {
  font-family: "Poppins";
  src: url(/fonts/poppins/Poppins-Light.ttf);
  font-weight: 300;
}

@font-face {
  font-family: "Poppins";
  src: url(/fonts/poppins/Poppins-SemiBold.ttf);
  font-weight: 500;
}

@font-face {
  font-family: "SourceSans3";
  src: url(/fonts/sourcesans/SourceSans3-Bold.ttf) format("truetype");
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "SourceSans3";
  src: url(/fonts/sourcesans/SourceSans3-Medium.ttf) format("truetype");
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

body {
  font-size: 0.9rem;
}

.logo-container {
  justify-content: center;
}

.logo {
  width: 5rem;
  margin: 2.5rem 0 4.375rem 0;
}

.headline-container {
  width: 100%;
}

.headline {
  font-family: "Poppins";
  display: flex;
  flex-direction: column;
  width: 100%;
  color: #000;
  line-height: 120%;
  font-size: 1.625rem;
}

.first-line {
  font-weight: 500;
}

.second-line {
  font-weight: 300;
}

.main-container {
  height: auto;
  width: 100%;
  display: flex;
  flex-direction: column;
  flex: 1 1 auto;
  gap: 5vh;
  padding-bottom: 1rem;
  position: relative;
}

.main-container:before {
  content: " ";
  display: block;
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  opacity: 0.9;
  background-image: url("https://firebasestorage.googleapis.com/v0/b/carrefour-jardin/o/images%2Fbg-carrefour-jardin-lp.webp?alt=media");
  background-repeat: no-repeat;
  background-position: 50% 0;
  background-size: cover;
  z-index: -10;
}

.main-container h2 {
  padding-left: 1.875rem;
  padding-right: 1.875rem;
}

.ar-button-container {
  position: relative;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
  width: fit-content;
  margin-bottom: 3rem;
}

ar-button {
  min-width: 15rem;
  max-width: calc(100vw * 80 /100);
  height: 3.5rem;
  padding: 0.75rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  background-color: #5bb9be;
  border: solid 2px #fff;
  border-radius: 50px;
  font-family: "Poppins";
  font-weight: 600;
  font-size: 1rem;
}

ar-button span{
  max-width: calc(100vw * 60 /100);
  overflow: hidden;
  display:inline-block;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.instruction-container {
  background-color: rgba(248, 248, 248, 0.8);
  border-radius: 20px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 1.65rem 1.5rem;
  margin: 0 1.875rem;
}

.instruction-title {
  color: #254f9b;
  width: 100%;
  font-family: SourceSans3, sans-serif;
  font-size: 1.25rem;
  line-height: 1.5rem;
  font-weight: 700;
  text-align: left;
  letter-spacing: 0.02rem;
  margin-bottom: 0.75rem;
}

.separator {
  background-color: #c20016;
  height: 1px;
  width: 60%;
  margin-bottom: 0.75rem;
}

ul {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 1.125rem;
}

ul p {
  color: #254f9b;
  font-family: SourceSans3, sans-serif;
  font-size: 1rem;
  line-height: 1.6rem;
  font-weight: 500;
  letter-spacing: 0.02rem;
  text-transform: none;
}

.watermark {
  position: absolute;
  width: 100%;
  display: flex;
  justify-content: center;
  bottom: 0.3rem;
}

.pw-by-wp--container {
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.pw-by-wp--link {
  all: initial;
  font-family: "Poppins";
  font-size: 0.75rem;
  font-weight: 300;
  text-align: center;
  color: var(--font-color);
}

.pw-by-wp--shadow {
  width: 280px;
}
