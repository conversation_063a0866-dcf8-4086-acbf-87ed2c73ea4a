import fs from "node:fs";
import { dirname, join } from "path";
import { fileURLToPath } from "url";

/**
 * @typedef {"landings" | "fallbacks" | "bundles"} ViewType
 */

const defaultTemplate = "wp-2";

/**
 * @param {ViewType} type
 * @param {string} templateName
 * @returns
 */
export default async function getViewOrDefault(type, templateName) {
  const templatePath = join(
    dirname(fileURLToPath(import.meta.url)),
    "..",
    "..",
    "views",
    "templates",
    templateName,
    `${templateName}.${getSuffix(type)}.ejs`
  );

  try {
    await fs.promises.access(templatePath, fs.constants.F_OK);
    return `views/templates/${templateName}/${templateName}.${getSuffix(type)}.ejs`;
  } catch (error) {
    return `views/templates/${defaultTemplate}/${defaultTemplate}.${getSuffix(type)}.ejs`
  }
}

/**
 * @param {ViewType} type
 */
function getSuffix(type) {
  switch (type) {
    case "landings":
      return "landing";

    case "fallbacks":
      return "fallback";

    case "bundles":
      return "bundle";

    default:
      return "landing";
  }
}
