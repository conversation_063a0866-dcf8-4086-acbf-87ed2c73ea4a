:root {
  --ar-button-color: #c6398d;
  --ar-button-hover: #d43d97;
}

@font-face {
  font-family: "Trueno";
  src: url(/fonts/trueno/TruenoSBd.ttf) format("truetype");
  font-weight: 600;
}

@font-face {
  font-family: "ProximaNovaCond";
  src: url(/fonts/proxima-nova/ProximaNovaCondBold.ttf) format("truetype");
  font-weight: 700;
}

@font-face {
  font-family: "ProximaNova";
  src: url(/fonts/proxima-nova/ProximaNovaBold.ttf) format("truetype");
  font-weight: 700;
}

@font-face {
  font-family: "ProximaNova";
  src: url(/fonts/proxima-nova/ProximaNovaRegular.ttf) format("truetype");
  font-weight: 400;
}

.content-mobile {
  justify-content: space-evenly;
}

.logo-container {
  margin-top: 5vh;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 1;
}

.background-header-image {
  position: absolute;
  top: -3vh;
  left: -26%;
  max-width: 272px;
  max-height: 206px;
  opacity: 0.3;
}

.background-footer-image {
  position: absolute;
  bottom: -5.21vh;
  right: 0px;
  max-width: 190px;
  max-height: 268px;
  opacity: 0.2;
}

.name-container-desktop {
  font-family: "ProximaNova", sans-serif !important;
}

.logo {
  width: 5rem;
}

.main-container {
  padding: 24px;
}

.headline {
  font-family: "ProximaNovaCond", sans-serif;
  color: #005aa7;
  width: auto;
}

.instruction-container {
  align-items: center;
}

.name-container h3 {
  font-family: "ProximaNova", sans-serif;
  font-weight: 400;
  font-size: 0.938rem;
  line-height: normal;
}

.instruction-title {
  font-family: "ProximaNova", sans-serif;
  font-weight: 700;
  min-width: 16.25rem;
  font-size: 1.375rem;
  margin-bottom: 1.875rem;
}

ul {
  min-width: 16.25rem;
  width: auto;
}

ul p {
  font-family: "ProximaNova", sans-serif;
  font-weight: 400;
  font-size: 0.938rem;
  line-height: normal;
}

.video-container {
  padding: 0 3.25rem;
}

ar-button {
  width: 80vw;
  height: 2.625rem;
  border-radius: 32px;
  font-family: "Trueno";
}

.icon-ar:before {
  content: "\e806"; /* -> AR icon */
  font-size: 32px;
}
