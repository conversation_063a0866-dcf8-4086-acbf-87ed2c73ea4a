:root {
  --ar-icon-size: 1.75rem;
  --wp-grey: #6ABE28;
}

@font-face {
  font-family: "Poppins";
  src: url(/fonts/poppins/Poppins-SemiBold.ttf);
  font-weight: 600;
}

@font-face {
  font-family: "Trueno";
  src: url(/fonts/trueno/TruenoLt.ttf) format("truetype");
  font-weight: 400;
}

@font-face {
  font-family: "Trueno";
  src: url(/fonts/trueno/TruenoSBd.ttf) format("truetype");
  font-weight: 600;
}

* {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  color: #000;
}

html {
  height: 100%;
  width: 100%;
  background-color: #F2F2F2;
}

body {
  position: relative;
  margin: 0;
  font-size: 0.9rem;
  line-height: 1.25rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  min-height: 100vh;
  min-height: 100dvh;
  height: max-content;
  padding: 0;
}

.content-mobile {
  flex: 0 0 auto;
  display: flex;
  width: 100%;
  flex-direction: column;
  position: relative;
}

.product-name {
  font-family: "Poppins";
  font-weight: 600;
}

.name-container {
  background-color: rgba(108, 184, 1, 0.7);
  color: #000;
  width: 100vw;
  font-family: "Poppins";
  font-weight: 600;
  font-size: 1.125rem;
  line-height: 1.7rem;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0.9rem 0;
}

.headline {
  width: 80vw;
  font-family: "Trueno";
  font-weight: 600;
  color: #000;
  margin-top: 5.6rem;
  margin-bottom: 1rem;
}

.instruction-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  font-family: "Trueno";
  font-size: 14px;
}

.main-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: calc((100vh * 10 / 100) - 1rem);
  padding-bottom: 1rem;
  margin-bottom: 3rem;
  position: relative;
}

.ar-button-container {
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
  position: relative;
}

.ar-button-container {
  margin: 0 auto;
  width: fit-content;
}

ar-button {
  width: calc(100vw - (100vw * 10) / 100);
  height: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #000;
  background-color: white;
  border-radius: 32px;
  font-family: "Trueno";
  font-weight: 600;
  font-size: 1rem;
}

.logo {
  width: 33%;
  height: auto;
  margin: 2.2rem 0 3.45rem 2rem;
}

ul {
  width: 80vw;
}

.watermark {
  position: absolute;
  bottom: 0.3rem;
}
