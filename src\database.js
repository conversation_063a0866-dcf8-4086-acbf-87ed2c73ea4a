import postgres from "postgres";
import Product from "./models/Product.js";
import Template from "./models/Template.js";
import Bundle from "./models/Bundle.js";

// @ts-ignore
const sql = postgres(process.env.DATABASE_URL);

/**
 * @param {string} uid
 * @returns {Promise<Product | null>}
 */
export async function getProduct(uid) {
  try {
    const results = await sql`
      SELECT
        p.id,
        p.uid,
        p.name,
        p.code,
        p.default_lang,
        p.model_name,
        p.sketchfab_model_id,
        p.occlusion,
        p.allows_content_scaling,
        json_build_object(
          'id', o.id,
          'name', o.name,
          'status', o.status,
          'bucket', o.bucket,
          'templateId', o.template_id,
          'analyticsId', o.analytics_id
        ) AS organization,
        CASE
          WHEN COUNT(pi) > 0 THEN json_agg(row_to_json(pi))
          ELSE NULL 
        END AS locales 
      FROM 
        product p
        JOIN organization o ON p.organization_id = o.id
        LEFT JOIN product_i18n pi ON p.id = pi.product_id
      WHERE
        p.uid = ${uid}
      GROUP BY
        p.id, o.id;
    `;

    if (results.length === 0) {
      return null;
    }

    return Product.from(results.at(0));
  } catch (err) {
    console.error(err);
    return null;
  }
}

/**
 * @param {number} id
 * @returns {Promise<Template | null>}
 */
export async function getTemplateById(id) {
  try {
    const results = await sql`
      SELECT
        t.id,
        t.name,
        json_agg(row_to_json(ti)) AS locales
      FROM
        template t
        JOIN template_i18n ti ON t.id = ti.template_id
      WHERE
        t.id = ${id}
      GROUP BY
        t.id;
    `;

    if (results.length === 0) {
      return null;
    }

    return Template.from(results.at(0));
  } catch (err) {
    console.error(err);
    return null;
  }
}

/**
 * @param {string} name
 * @returns {Promise<Template | null>}
 */
export async function getTemplateByName(name) {
  try {
    const results = await sql`
      SELECT
        t.id,
        t.name,
        json_agg(row_to_json(ti)) AS locales
      FROM
        template t
        JOIN template_i18n ti ON t.id = ti.template_id
      WHERE
        t.name = ${name}
      GROUP BY
        t.id, t.name;
    `;

    if (results.length === 0) {
      return null;
    }

    return Template.from(results.at(0));
  } catch (err) {
    console.error(err);
    return null;
  }
}

/**
 * @param {number} id
 * @returns {Promise<Template | null>}
 */
export async function getTemplateOrDefault(id) {
  let template = await getTemplateById(id);

  if (!template) {
    template = await getTemplateByName("wp-2");
  }
  return template;
}

/**
 * @param {string} uid
 * @returns {Promise<Bundle | null>}
 */
export async function getBundle(uid) {
  try {
    const results = await sql`
      SELECT
        b.uid,
        b.name,
        json_build_object(
          'id', o.id,
          'name', o.name,
          'bucket', o.bucket,
          'templateId', o.template_id,
          'analyticsId', o.analytics_id
        ) AS organization,
        json_agg(
          json_build_object(
            'id', p.id,
            'uid', p.uid,
            'default_lang', p.default_lang,
            'name', p.name,
            'code', p.code,
            'model_name', p.model_name,
            'sketchfab_model_id', p.sketchfab_model_id,
            'occlusion', p.occlusion,
            'allows_content_scaling', p.allows_content_scaling,
            'organization', json_build_object(
              'id', o.id,
              'name', o.name,
              'bucket', o.bucket,
              'templateId', o.template_id,
              'analyticsId', o.analytics_id
            ),
            'locales', (
              SELECT json_agg(
                row_to_json(pi)
              )
              FROM product_i18n pi
              WHERE pi.product_id = p.id
            )
          )
        ) AS products
      FROM
        bundle b
        JOIN organization o ON b.organization_id = o.id
        LEFT JOIN m2m_bundle_product bp ON b.id = bp.bundle_id
        LEFT JOIN product p ON bp.product_id = p.id
      WHERE
        b.uid = ${uid}
      GROUP BY
        b.id, o.id
    `;

    if (results.length === 0) {
      return null;
    }

    return Bundle.from(results.at(0));
  } catch (err) {
    console.error(err);
    return null;
  }
}
