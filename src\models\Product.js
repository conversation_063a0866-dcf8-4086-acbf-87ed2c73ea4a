import ProductLocale from "./ProductLocale.js";
import Organization from "./Organization.js";

export default class Product {
  /**
   * @param {string} uid
   * @param {string} name
   * @param {string} code
   * @param {string} defaultLang
   * @param {string} sketchfabModelId
   * @param {boolean} occlusion
   * @param {boolean} allowsContentScaling
   * @param {Organization} organization
   * @param {Array<ProductLocale>} locales
   */
  constructor(
    uid,
    name,
    code,
    defaultLang,
    modelName,
    sketchfabModelId,
    occlusion,
    allowsContentScaling,
    organization,
    locales,
  ) {
    this.uid = uid;
    this.name = name;
    this.code = code;
    this.defaultLang = defaultLang;
    this.modelName = modelName;
    this.sketchfabModelId = sketchfabModelId;
    this.occlusion = occlusion;
    this.allowsContentScaling = allowsContentScaling;
    this.organization = organization;
    this.locales = locales;
  }

  get glbUrl() {
    const modelName = this.modelName ?? this.uid;
    return `https://assets.wonder-shop.net/${this.organization.bucket}/${modelName}.glb`;
  }

  get usdzUrl() {
    const modelName = this.modelName ?? this.uid;
    return `https://assets.wonder-shop.net/${this.organization.bucket}/${modelName}.usdz`;
  }

  /**
   * @param {*} data
   * @returns {Product | null}
   */
  static from(data) {
    try {
      return new Product(
        data.uid,
        data.name,
        data.code,
        data.default_lang,
        data.model_name,
        data.sketchfab_model_id,
        data.occlusion,
        data.allows_content_scaling,
        Organization.from(data.organization),
        data.locales?.map((l) => ProductLocale.from(l)) ?? [],
      );
    } catch (error) {
      console.error("Failed to parse product", data, error);
      return null;
    }
  }
}
