/* eslint-disable import/no-extraneous-dependencies */
import chalk from "chalk";
import fs from "fs";
import ncp from "ncp";
import Listr from "listr";
import { promisify } from "util";

const access = promisify(fs.access);
const { rename } = fs;
const { readFile } = fs;
const copy = promisify(ncp);

async function copyTemplateFiles(options) {
  try {
    await access(options.templateTargetDirectory);
    console.error(
      "%s A template with that name already exists",
      chalk.red.bold("ERROR")
    );
    process.exit(1);
  } catch (err) {
    return copy(options.templateDirectory, options.templateTargetDirectory);
  }
  return true;
}

async function copyCssFiles(options) {
  try {
    await access(options.cssTargetDirectory);
    console.error(
      "%s A CSS folder with that name already exists",
      chalk.red.bold("ERROR")
    );
    process.exit(1);
  } catch (err) {
    return copy(options.cssDirectory, options.cssTargetDirectory);
  }
  return true;
}

async function renameTemplateFiles(options) {
  try {
    await access(options.templateTargetDirectory);
    rename(
      `${options.templateTargetDirectory}/template.landing.ejs`,
      `${options.templateTargetDirectory}/${options.templateName}.landing.ejs`,
      (err) => {
        if (err) console.log(`ERROR: ${err}`);
      }
    );
    rename(
      `${options.templateTargetDirectory}/template.fallback.ejs`,
      `${options.templateTargetDirectory}/${options.templateName}.fallback.ejs`,
      (err) => {
        if (err) console.log(`ERROR: ${err}`);
      }
    );
    rename(
      `${options.templateTargetDirectory}/template.bundle.ejs`,
      `${options.templateTargetDirectory}/${options.templateName}.bundle.ejs`,
      (err) => {
        if (err) console.log(`ERROR: ${err}`);
      }
    );
  } catch (err) {
    console.error("%s Can not rename files", chalk.red.bold("ERROR"));
    process.exit(1);
  }
  return true;
}

async function renameCssFiles(options) {
  try {
    await access(options.cssTargetDirectory);
    rename(
      `${options.cssTargetDirectory}/landing.css`,
      `${options.cssTargetDirectory}/${options.templateName}.css`,
      (err) => {
        if (err) console.log(`ERROR: ${err}`);
      }
    );
    rename(
      `${options.cssTargetDirectory}/fallback.css`,
      `${options.cssTargetDirectory}/${options.templateName}-fallback.css`,
      (err) => {
        if (err) console.log(`ERROR: ${err}`);
      }
    );
  } catch (err) {
    console.error("%s Can not rename CSS files", chalk.red.bold("ERROR"));
    process.exit(1);
  }
  return true;
}

async function rewriteTemplateFiles(options) {
  const cssPath = options.cssTargetDirectory.replace("public", "");
  try {
    await access(options.templateTargetDirectory);
    readFile(
      `${options.templateTargetDirectory}/${options.templateName}.landing.ejs`,
      "utf8",
      (err, data) => {
        if (err) {
          return console.log(err);
        }
        let result = data.replace(
          /%%%cssPath%%%/g,
          `${cssPath}/${options.templateName}.css`
        );
        result = result.replace(/%%%theme%%%/g, options.templateTheme);

        fs.writeFile(
          `${options.templateTargetDirectory}/${options.templateName}.landing.ejs`,
          result,
          "utf8",
          (error) => {
            if (error) return console.log(error);
            return true;
          }
        );
        return true;
      }
    );
    readFile(
      `${options.templateTargetDirectory}/${options.templateName}.fallback.ejs`,
      "utf8",
      (err, data) => {
        if (err) {
          return console.log(err);
        }
        let result = data.replace(
          /%%%cssPath%%%/g,
          `${cssPath}/${options.templateName}-fallback.css`
        );
        result = result.replace(/%%%theme%%%/g, options.templateTheme);

        fs.writeFile(
          `${options.templateTargetDirectory}/${options.templateName}.fallback.ejs`,
          result,
          "utf8",
          (error) => {
            if (error) return console.log(error);
            return true;
          }
        );
        return true;
      }
    );
    readFile(
      `${options.templateTargetDirectory}/${options.templateName}.bundle.ejs`,
      "utf8",
      (err, data) => {
        if (err) {
          return console.log(err);
        }
        let result = data.replace(
          /%%%cssPath%%%/g,
          `${cssPath}/${options.templateName}.css`
        );
        result = result.replace(/%%%theme%%%/g, options.templateTheme);

        fs.writeFile(
          `${options.templateTargetDirectory}/${options.templateName}.bundle.ejs`,
          result,
          "utf8",
          (error) => {
            if (error) return console.log(error);
            return true;
          }
        );
        return true;
      }
    );
  } catch (err) {
    console.error(
      "%s A template with that name already exists",
      chalk.red.bold("ERROR")
    );
    process.exit(1);
  }
  return true;
}

export default async function createTemplate(options) {
  const tasks = new Listr(
    [
      {
        title: "Copy template files",
        task: () => copyTemplateFiles(options),
      },
      {
        title: "Copy CSS files",
        task: () => copyCssFiles(options),
      },
      {
        title: "Rename template files",
        task: () => renameTemplateFiles(options),
      },
      {
        title: "Rename CSS files",
        task: () => renameCssFiles(options),
      },
      {
        title: "Link files",
        task: () => rewriteTemplateFiles(options),
      },
    ],
    {
      exitOnError: true,
    }
  );

  await tasks.run();
  console.log("%s template ready", chalk.green.bold("DONE"));
  return true;
}

/**
 * @typedef Organization
 * @property {string} id
 * @property {string} name
 */
