:root {
  --ar-icon-size: 1.75rem;
  --red: #e32218;
  --ar-button-hover: #b4160e;
}

@font-face {
  font-family: "Poppins";
  src: url(/fonts/poppins/Poppins-SemiBold.ttf);
  font-weight: 600;
}
@font-face {
  font-family: "SourceSans3";
  src: url(/fonts/sourcesans/SourceSans3-Bold.ttf) format("truetype");
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

* {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  color: black;
}
html {
  height: 100%;
  width: 100%;
  background-color: #ffffff;
}
body {
  margin: 0;
  font-size: 0.9rem;
  line-height: 1.25rem;
  display: flex;
  flex-direction: column;
  align-items: left;
  justify-content: space-between;
  height: 100vh;
  height: 100dvh;
  padding: 0 2rem;
}
.headline {
  width: 100%;
  font-family: "Poppins";
  font-weight: 600;
  font-size: 1.6rem;
  line-height: 2.1rem;
  padding: 1rem 0 1.5rem 0;
}
a {
  text-decoration: none;
}
.logo-container {
  margin: 0;
  max-width: 100%;
  object-fit: contain;
  align-items: left;
}
.logo {
  width: 4.375rem;
  height: 4.375rem;
  margin: 2.5rem 0 1.3rem 0;
}

.ar-button-container {
  margin: 0 auto;
}

ar-button {
  width: 16rem;
  height: 3.2rem;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;

  border-radius: 32px;
  border: solid 3px #ffffffb6;
  background-color: var(--red);
  font-family: "Poppins";
  font-weight: 600;
  font-size: 1rem;
  margin: 1.2rem 0;
}

ar-button:hover {
  background-color: var(--ar-button-hover);
}

.instruction-title {
  width: 60%;
  padding-bottom: 0.5rem;
  border-style: none none solid;
  border-width: 1px;
  border-color: #000 #000 var(--red);
  font-family: SourceSans3, sans-serif;
  font-size: 1.25rem;
  line-height: 1.5rem;
  font-weight: 700;
  text-align: left;
  letter-spacing: 0.02rem;
  margin-bottom: 0.6rem;
}

ul p {
  margin-bottom: 0.3rem;
  font-family: SourceSans3, sans-serif;
  font-size: 0.8rem;
  line-height: 1.2rem;
  font-weight: 700;
  letter-spacing: 0.02rem;
  text-transform: none;
}
