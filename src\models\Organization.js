// @ts-check

/**
 * Represents a 128-bit universally unique identifier (UUID).
 * @typedef {string} UUID
 */

export default class Organization {
  /**
   * @param {Object} config
   * @param {string} config.name
   * @param {'active' | 'inactive'} config.status
   * @param {string} config.bucket
   * @param {number} config.templateId
   * @param {UUID} config.analyticsId
   */
  constructor({ name, status, bucket, templateId, analyticsId }) {
    this.name = name;
    this.status = status;
    this.bucket = bucket;
    this.templateId = templateId;
    this.analyticsId = analyticsId;
  }

  get logoUrl() {
    return `https://firebasestorage.googleapis.com/v0/b/${this.bucket}/o/images%2Flogo.webp?alt=media`;
  }

  get faviconUrl() {
    return `https://firebasestorage.googleapis.com/v0/b/${this.bucket}/o/images%2Ffavicon.ico?alt=media`;
  }

  get webclipUrl() {
    return `https://firebasestorage.googleapis.com/v0/b/${this.bucket}/o/images%2Fwebclip.png?alt=media`;
  }

  /**
   * @param {*} data
   * @returns {Organization}
   */
  static from(data) {
    return new Organization({
      name: data.name,
      status: data.status,
      bucket: data.bucket,
      templateId: data.templateId,
      analyticsId: data.analyticsId,
    });
  }
}
