// @ts-check

export default class IntlProvider {
  /**
   * Semi-column separated string containing all the user accepted languages.
   * @type {string}
   * @example "fr-FR,fr;q=0.9,en-US;q=0.8,en;q=0.7"
   */
  #browserAcceptLanguage;

  /**
   * Array of ISO codes (language code + region code) accepted by the user's browser.
   * @type {string[]}
   * @example ["fr-FR", "en-US"]
   */
  #acceptIsoCodes;

  /**
   * Array of language codes accepted by the user's browser.
   * @type {string[]}
   * @example ["fr", "en"]
   */
  #acceptLangCodes;

  /**
   * The default language ISO code.
   * @type {string}
   * @example "fr-FR"
   */
  #defaultLanguage;

  /**
   * The default language code.
   * @type {string}
   * @example "fr"
   */
  #defaultLangCode;

  /**
   * @param {Object} config
   * @param {string} config.browserAcceptLanguage
   * @param {string} config.defaultLanguage
   */
  constructor({ browserAcceptLanguage, defaultLanguage }) {
    this.#browserAcceptLanguage = browserAcceptLanguage ?? defaultLanguage;
    this.#defaultLanguage = defaultLanguage;

    this.#defaultLangCode = defaultLanguage.split("-")[0] ?? "fr";
    this.#acceptIsoCodes = this.#browserAcceptLanguage
      .split(",")
      .map((x) => x.split(";")[0] || "");
    // using a Set to de-dup values and converting it back to an array using the spread operator.
    this.#acceptLangCodes = [
      ...new Set(this.#acceptIsoCodes.map((x) => x?.split("-")[0]) || ""),
    ];
  }

  /**
   * Try to find the best product locale available or fallback to the default locale.
   * @param {{ code: string }[]} from
   * @param {*} def
   * @returns {*}
   */
  findBestOr(from, def) {
    // Languages that are available for the product or template. Can be ISO codes or language codes.
    // @example ["fr", "en-US"]
    const availableLocales = from.map((locale) => locale.code);

    /** @type {string} */
    const bestLocale =
      this.#acceptIsoCodes.filter((code) => availableLocales.includes(code))[0] ||
      this.#acceptLangCodes.filter((code) => availableLocales.includes(code))[0] ||
      availableLocales.find((code) => code.includes(this.#defaultLanguage)) ||
      availableLocales.find((code) => code.includes(this.#defaultLangCode)) ||
      this.#defaultLanguage;

    return from.find((locale) => locale.code === bestLocale) ?? def;
  }
}
