:root {
  --ar-icon-size: 1.75rem;
  --ariel-color: #000;
  --font-color: #FFF;
  --ar-button-color: #002F6C;
}

@font-face {
  font-family: "Trueno";
  src: url(/fonts/trueno/TruenoSBd.ttf) format("truetype");
  font-weight: 600;
}

@font-face {
  font-family: 'Poppins';
  src: url(/fonts/poppins/Poppins-Light.ttf);
  font-weight: 300;
}

@font-face {
  font-family: "Open Sans";
  src: url(/fonts/opensans/OpenSans-Light.ttf) format("truetype");
  font-weight: 300;
}

@font-face {
  font-family: "Open Sans";
  src: url(/fonts/opensans/OpenSans-Regular.ttf) format("truetype");
  font-weight: 400;
}

@font-face {
  font-family: "Open Sans";
  src: url(/fonts/opensans/OpenSans-SemiBold.ttf) format("truetype");
  font-weight: 700;
}

@font-face {
  font-family: "Open Sans";
  src: url(/fonts/opensans/OpenSans-Medium.ttf) format("truetype");
  font-weight: 600;
}

body {
  font-size: 0.9rem;
  height: max-content;
}

.logo {
  width: 5rem;
  margin: 2.5rem 0 3.45rem 1.875rem;
}

.content-mobile {
  height: 100vh;
  height: 100dvh;
}

.headline {
  font-family: "Open Sans";
  font-weight: 700;
  color: #000;
  font-size: 1.625rem;
}

.instruction-container {
  padding-left: 1.875rem;
  padding-right: 1.875rem;
}

.main-container {
  gap: 4.375rem;
  padding-top: 3rem;
  padding-bottom: 1rem;
}

.main-container h2 {
  padding-left: 1.875rem;
  padding-right: 1.875rem;
}

.main-container:before {
  content: " ";
  display: block;
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  opacity: 0.15;
  background-image: url("https://firebasestorage.googleapis.com/v0/b/vulcano/o/images%2Fbg-vulcano.svg?alt=media");
  background-repeat: no-repeat;
  background-position: 50% 0;
  background-size: 140%;
  z-index: -10;
}

ar-button {
  width: calc(100vw - (100vw * 10) / 100);
  height: 3.125rem;
  background-color: var(--ar-button-color);
  border: solid 2px #fff;
  border-radius: 50px;
  font-family: "Open Sans";
}

ul p {
  width: 100%;
  font-size: 0.875rem;
  line-height: 1.125rem;
  font-family: 'Open Sans';
  font-weight: 400;
  color: #000;
}
