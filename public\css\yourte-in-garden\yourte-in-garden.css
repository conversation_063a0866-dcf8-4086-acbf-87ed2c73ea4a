:root {
  --ar-button-color: #f15025;
  --ar-button-hover: #f15025;
}

@font-face {
  font-family: "Nunito";
  src: url(/fonts/Nunito/Nunito-VariableFont_wght.ttf) format("truetype");
  font-weight: 400;
}

@font-face {
  font-family: "FunnelDisplay";
  src: url(/fonts/FunnelDisplay/FunnelDisplay-VariableFont_wght.ttf)
    format("truetype");
  font-weight: 700;
}

* {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}

.logo-container-desktop {
  background: #dfdece !important;
}

.name-container-desktop {
  font-family: "FunnelDisplay", sans-serif !important;
  background: none !important;
}

.content-mobile {
  justify-content: space-evenly;
}

body {
  background-color: #dfdece;
}

.logo-container {
  margin-top: 5vh;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 1;
}

.logo {
  width: 201px;
}

.main-container {
  padding: 24px;
}

.headline {
  font-family: "FunnelDisplay", sans-serif;
  color: #112c22;
  width: auto;
}

.headline-desktop {
  font-family: "FunnelDisplay", sans-serif !important;
}

.name-container h3 {
  font-family: "Nunito", sans-serif;
  font-weight: 400;
  font-size: 0.938rem;
  line-height: normal;
}

.instruction-title {
  font-family: "FunnelDisplay", sans-serif;
  font-weight: 700;
  min-width: 16.25rem;
  font-size: 1.375rem;
  margin-bottom: 1.875rem;
  color: #112c22;
}

ul {
  min-width: 16.25rem;
  width: auto;
}

ul p {
  font-family: "Nunito", sans-serif;
  font-weight: 400;
  font-size: 0.938rem;
  line-height: normal;
  color: #112c22;
}

.video-container {
  padding: 0 3.25rem;
}

ar-button {
  width: 80vw;
  max-width: 210px;
  height: 2.625rem;
  border-radius: 12px;
  font-family: "FunnelDisplay", sans-serif;
  font-size: 16px;
  font-weight: 700;
  background-color: #f15025;
}

.icon-ar:before {
  content: ""; /* -> AR icon */
}

.icon-ar:after {
  font-family: "fontello";
  font-style: normal;
  font-weight: normal;
  display: inline-block;
  text-decoration: inherit;
  width: auto;
  margin-left: 0.5rem;
  text-align: center;
  font-variant: normal;
  text-transform: none;
  font-size: 150%;
  line-height: 100%;
  -webkit-font-smoothing: antialiased;

  content: "\e806"; /* -> AR icon */
  font-size: 25px;
}
