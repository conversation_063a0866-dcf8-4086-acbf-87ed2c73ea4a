// @ts-check

export default class ProductLocale {
  /**
   * @param {Object} config
   * @param {string} config.code
   * @param {string | undefined} config.title
   * @param {string | undefined} config.link
   * @param {string | undefined} config.checkoutTitle
   * @param {string | undefined} config.checkoutSubtitle
   * @param {string | undefined} config.callToAction
   * @param {string | undefined} config.canonicalWebPageUrl
   * @param {string | undefined} config.iosLink
   * @param {string | undefined} config.price
   * @param {string | undefined} config.buttonName
   */
  constructor({
    code,
    title,
    link,
    checkoutTitle,
    checkoutSubtitle,
    callToAction,
    canonicalWebPageUrl,
    iosLink,
    price,
    buttonName,
  }) {
    this.code = code;
    this.title = title;
    this.link = link;
    this.checkoutTitle = checkoutTitle;
    this.checkoutSubtitle = checkoutSubtitle || "ㅤ";
    this.callToAction = callToAction;
    this.canonicalWebPageUrl = canonicalWebPageUrl;
    this.iosLink = iosLink;
    this.price = price;
    this.buttonName = buttonName;
  }

  /**
   * @param {*} data
   * @returns {ProductLocale}
   */
  static from(data) {
    return new ProductLocale({
      code: data.locale_code,
      title: data.title,
      link: data.link,
      checkoutTitle: data.checkout_title,
      checkoutSubtitle: data.checkout_subtitle || "ㅤ",
      callToAction: data.call_to_action,
      canonicalWebPageUrl: data.canonical_web_page_url,
      iosLink: data.ios_link || data.link,
      price: data.price,
      buttonName: data.button_name,
    });
  }

  static default() {
    return new ProductLocale({
      code: 'fr',
      title: undefined,
      link: undefined,
      checkoutTitle: undefined,
      checkoutSubtitle: undefined,
      callToAction: undefined,
      canonicalWebPageUrl: undefined,
      iosLink: undefined,
      price: undefined,
      buttonName: 'Voir chez moi',
    });
  }
}
