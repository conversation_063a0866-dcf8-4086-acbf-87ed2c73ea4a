:root {
  --ar-button-color: #EE7BAE;
  --ar-button-hover: #b65d85;
}

@font-face {
  font-family: "Trueno";
  src: url(/fonts/trueno/TruenoSBd.ttf) format("truetype");
  font-weight: 600;
}

@font-face {
  font-family: "ProximaNovaCond";
  src: url(/fonts/proxima-nova/ProximaNovaCondBold.ttf) format("truetype");
  font-weight: 700;
}

@font-face {
  font-family: "ProximaNova";
  src: url(/fonts/proxima-nova/ProximaNovaBold.ttf) format("truetype");
  font-weight: 700;
}

@font-face {
  font-family: "ProximaNova";
  src: url(/fonts/proxima-nova/ProximaNovaRegular.ttf) format("truetype");
  font-weight: 400;
}

.content-mobile {
  justify-content: space-evenly;
}

.logo-container {
  margin-top: 5vh;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.logo {
  width: 5rem;
}

.main-container {
  padding-bottom: 3rem;
  background-image: url("https://firebasestorage.googleapis.com/v0/b/carrefour-jardin-24/o/images%2Fcitronnier.webp?alt=media");
  background-repeat: no-repeat;
  background-position: 0 -3vh;
}

.name-container-desktop {
  font-family: "ProximaNova", sans-serif !important;
}

.headline {
  font-family: "ProximaNovaCond", sans-serif;
  color: #005aa7;
  width: auto;
}

.instruction-container {
  align-items: center;
}

.instruction-title {
  font-family: "ProximaNova", sans-serif;
  font-weight: 700;
  min-width: 16.25rem;
  font-size: 1.375rem;
  margin-bottom: 1.875rem;
  width: auto;
}

ul {
  min-width: 16.25rem;
  width: auto;
}

ul p {
  font-family: "ProximaNova", sans-serif;
  font-weight: 400;
  font-size: 0.938rem;
  line-height: normal;
}

.video-container {
  padding: 0 3.25rem;
}

ar-button {
  width: 80vw;
  height: 2.625rem;
  border-radius: 32px;
  font-family: "Trueno";
}
