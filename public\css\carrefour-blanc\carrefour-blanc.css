:root {
  --ar-icon-size: 1.75rem;
  --button: #F21C72;
  --button-hover: #ce1660;
}

@font-face {
  font-family: "Ubuntu";
  src: url(/fonts/ubuntu/Ubuntu.ttf) format("truetype");
  font-weight: 400;
}

@font-face {
  font-family: "Ubuntu";
  src: url(/fonts/ubuntu/Ubuntu-Bold.ttf) format("truetype");
  font-weight: 700;
}

@font-face {
  font-family: "Open Sans";
  src: url(/fonts/opensans/OpenSans-Medium.ttf) format("truetype");
  font-weight: 600;
}

body {
  font-size: 0.9rem;
}

.main-container::before{
  content: " ";
  display: block;
  position: absolute;
  left: 0;
  top: 10rem;
  width: 100%;
  height: 100%;
  opacity: 0.7;
  background: url("https://firebasestorage.googleapis.com/v0/b/carrefour-blanc/o/images%2Fbg-carrefour-blanc.webp?alt=media");
  background-repeat: no-repeat;
  background-position: 50% 0;
  background-size: cover;
  mix-blend-mode: hard-light;
  z-index: -1;
}

.main-container {
  gap: 4.1rem;
  padding-bottom: 1rem;
  overflow: hidden;
}

.logo {
  width: 5rem;
  margin: 2.5rem 0 3rem 1.875rem;
}

.logo-container {
  text-align: left;
}

.headline {
  font-family: "Ubuntu";
  font-weight: 700;
  color: #1438A7;
  line-height: 120%;
  font-size: 1.625rem;
  margin-bottom: 1rem;
}

.instruction-container {
  padding-left: 1.875rem;
  padding-right: 1.875rem;
  padding-bottom: 4rem;
}

.main-container h2 {
  padding-left: 1.875rem;
  padding-right: 1.875rem;
}

ar-button {
  width: calc(100vw - (100vw * 10) / 100);
  height: 3.125rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  background-color: var(--button);
  border: solid 2px #fff;
  border-radius: 50px;
  font-family: "Open Sans";
  font-weight: 600;
  font-size: 1rem;
}

ar-button:hover {
  background-color: var(--button-hover);
}

.instruction-title {
  width: 100%;
  font-size: 0.875rem;
  line-height: 1.125rem;
  font-family: 'Ubuntu';
  font-weight: 400;
  letter-spacing: 0.02rem;
  color: #000;
}

ul p {
  font-size: 0.875rem;
  line-height: 1.125rem;
  font-family: 'Ubuntu';
  font-weight: 400;
  color: #000;
}
