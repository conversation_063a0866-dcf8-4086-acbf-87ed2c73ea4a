<style>
  .content-desktop {
    flex: 0 0 auto;
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100vh;
    height: 100dvh;
    overflow: hidden;
  }

  .background-container {
    position: absolute;
    width: 100%;
    height: 100%;
    background-image: var(--bgImgUrl);
    background-repeat: no-repeat;
    background-position: 100% 50%;
    background-size: contain;
  }

  .logo-container-desktop {
    width: 100%;
    min-height: 11rem;
    height: 21.5rem;
    display: flex;
    align-items: center;
    background: linear-gradient(180deg,
        var(--ar-button-color) 5.94%,
        var(--ar-button-hover) 71.44%,
        var(--ar-button-color) 207.46%);
  }

  .logo-desktop {
    height: 8rem;
    padding: 0 12vw;
    z-index: 0;
  }

  .main-container-desktop {
    position: relative;
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4.375rem;
    padding: 3rem 12vw;
  }

  .flex-container-desktop {
    display: flex;
    justify-content: center;
    width: 100%;
    gap: 1.8rem;
    position: relative;
    height: 90%;
  }

  .instruction-container-desktop {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 90%;
    z-index: 10;
  }

  .name-container-desktop {
    background-color: rgba(255, 255, 255, 0.2);
    color: var(--ar-button-color);
    width: 100%;
    font-family: "Poppins";
    font-weight: 600;
    font-size: 2.5rem;
    line-height: normal;
    padding: 0.9rem 0;
    margin-bottom: 1rem;
  }

  .name-container-desktop:empty {
    visibility: hidden;
  }

  .redirect-container {
    display: flex;
    align-items: center;
    gap: 2rem;
    width: 100%;
  }

  .qr-code-container {
    display: flex;
    align-items: center;
    position: relative;
    padding: 1.125rem;
    background: #fff;
    background-clip: padding-box;
    border: solid 6px transparent;
    border-radius: 2.5rem;
    min-width: 13rem;
    min-height: 13rem;
  }

  .qr-code-container:before {
    content: "";
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: -10;
    margin: -7px;
    border-radius: inherit;
    background: linear-gradient(180deg,
        var(--ar-button-hover) 0%,
        var(--ar-button-color) 32.5%,
        var(--ar-button-hover) 100%);
  }

  #qr-code {
    width: 11.25rem;
    height: auto;
  }

  .headline-desktop {
    color: #000;
    font-family: "Trueno";
    font-size: 1.875rem;
    line-height: 2.25rem;
  }

  .viewer-container {
    flex: 1;
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 100%;
    min-width: 50%;
    min-height: fit-content;
  }

  .sketchfab-embed-wrapper {
    overflow: hidden;
    position: relative;
    width: 100%;
    height: 100%;
    background-color: #fff;
    border-radius: 1.25rem;
    box-shadow: 0px 0px 4px 0px #00000040;
  }

  .sketchfab-embed-wrapper model-viewer {
    width: 100%;
    min-height: 100%;
  }

  .watermark-desktop {
    position: absolute;
    bottom: 0;
    padding: 0 12vw;
    width: 100%;
    display: flex;
    justify-content: flex-end;
    gap: 4rem;
    color: #000;
    font-family: "Trueno";
  }

  .legals-button {
    color: #282828;
    font-family: "Trueno";
    font-size: 0.75rem;
  }

  @media screen and (max-width: 1400px) {
    .instruction-container-desktop {
      flex: 0 1 0;
    }
  }

  @media screen and (max-width: 1250px) {
    .content-desktop {
      overflow: auto;
    }

    .main-container-desktop {
      min-height: 100%;
    }

    .background-container {
      background-position: 50% 50%;
    }

    .flex-container-desktop {
      flex-direction: column;
      justify-content: space-between;
      min-height: fit-content;
    }

    .viewer-container {
      flex: 1;
      min-height: auto;
    }

    .watermark-desktop {
      justify-content: center;
    }
  }

  @media screen and (orientation: portrait) {
    .background-container {
      background-position: 50% 50%;
    }

    .flex-container-desktop {
      flex-direction: column;
      justify-content: space-between;
    }

    .instruction-container-desktop {
      flex: 1;
    }

    .watermark-desktop {
      justify-content: center;
    }
  }
</style>

<main class="content-desktop hidden">
  <div class="background-container"></div>
  <div class="logo-container-desktop">
    <img class="logo-desktop" src="<%= logo %>" alt="logo" onerror="this.onerror=null; this.src=''; this.style='display:none'" />
  </div>


  <section class="main-container-desktop">
    <div class="flex-container-desktop">
      <article class="instruction-container-desktop">
        <div class="name-container-desktop"><% if (locals.title) { %> <%= title %> <% } else if
          (locals.bundleName) { %> <%= bundleName %> <% } %></div>
        <div class="redirect-container">
          <div class="qr-code-container">
            <img class="hidden" id="qr-code" src="" alt="QR Code" />
          </div>
          <h2 class="headline-desktop"><%- fallbackTranslations.text %></h2>
        </div>
        <div>
          <img src="/images/shadow-desktop.webp" />
        </div>
      </article>

      <% if (viewer && modelName) { %>
      <div class="viewer-container">
        <%- include('../partials/google-viewer.ejs', {controls: false}) %>
      </div>
      <% } %>
    </div>
    <div class="watermark-desktop">
      <%- include('../partials/powered-by-wp.ejs', {theme: 'dark' }) %>
      <a href="#" class="legals-button"><%- fallbackTranslations.legals %></a>
    </div>
  </section>
</main>